{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 上海陆家嘴CBD周边住宅分析 - 快速开始\n", "\n", "本notebook展示如何使用项目进行数据收集、处理和分析。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. 环境设置"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import sys\n", "import os\n", "from pathlib import Path\n", "\n", "# 添加项目根目录到Python路径\n", "project_root = Path().absolute().parent\n", "sys.path.insert(0, str(project_root))\n", "\n", "print(f\"项目根目录: {project_root}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import logging\n", "\n", "# 设置中文字体\n", "plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS']\n", "plt.rcParams['axes.unicode_minus'] = False\n", "\n", "# 设置日志\n", "logging.basicConfig(level=logging.INFO)\n", "\n", "print(\"环境设置完成\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. 配置检查"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from src.utils.config_loader import config\n", "\n", "# 检查配置\n", "print(\"配置信息:\")\n", "print(f\"CBD名称: {config.get('CBD_INFO', 'name')}\")\n", "print(f\"CBD坐标: ({config.getfloat('CBD_INFO', 'longitude')}, {config.getfloat('CBD_INFO', 'latitude')})\")\n", "\n", "# 检查API密钥\n", "api_key = config.get('API_KEYS', 'gaode_ak')\n", "if api_key == 'YOUR_GAODE_API_KEY_HERE':\n", "    print(\"⚠️ 警告: 请在 config/config.ini 中配置有效的高德API密钥\")\n", "else:\n", "    print(f\"✅ API密钥已配置: {api_key[:10]}...\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. 数据收集示例（小规模测试）"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 注意：这里只是演示，实际运行需要有效的API密钥\n", "from src.utils.api_helpers import geocode_address_gaode\n", "\n", "# 测试地理编码\n", "test_address = \"上海市浦东新区陆家嘴环路1000号\"\n", "print(f\"测试地址: {test_address}\")\n", "\n", "try:\n", "    coords = geocode_address_gaode(test_address)\n", "    if coords:\n", "        print(f\"地理编码结果: 经度={coords[0]}, 纬度={coords[1]}\")\n", "    else:\n", "        print(\"地理编码失败\")\n", "except Exception as e:\n", "    print(f\"地理编码测试失败: {e}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. 数据分析示例"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 如果已有数据，加载并分析\n", "from src.utils.file_io import load_dataframe, get_data_path\n", "\n", "# 尝试加载最终数据集\n", "final_data_path = get_data_path(\"data/final/final_merged_dataset.csv\")\n", "\n", "if os.path.exists(final_data_path):\n", "    df = load_dataframe(final_data_path)\n", "    print(f\"数据加载成功，形状: {df.shape}\")\n", "    print(f\"列名: {list(df.columns)}\")\n", "    \n", "    # 显示基本统计信息\n", "    print(\"\\n数据概览:\")\n", "    print(df.head())\n", "    \n", "else:\n", "    print(\"最终数据集不存在，请先运行数据收集流程\")\n", "    print(\"运行命令: python src/main.py\")\n", "    \n", "    # 创建示例数据用于演示\n", "    np.random.seed(42)\n", "    n_samples = 100\n", "    \n", "    df = pd.DataFrame({\n", "        'title': [f'小区{i}' for i in range(n_samples)],\n", "        'unit_price': np.random.normal(60000, 20000, n_samples),\n", "        'commute_duration_min': np.random.normal(45, 15, n_samples),\n", "        'convenience_index': np.random.normal(60, 20, n_samples),\n", "        'distance_to_cbd_km': np.random.exponential(10, n_samples)\n", "    })\n", "    \n", "    print(\"使用示例数据进行演示\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. 数据可视化"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 价格分布\n", "if 'unit_price' in df.columns:\n", "    plt.figure(figsize=(12, 4))\n", "    \n", "    plt.subplot(1, 2, 1)\n", "    plt.hist(df['unit_price'].dropna(), bins=30, alpha=0.7)\n", "    plt.title('房价分布')\n", "    plt.xlabel('单价 (元/平米)')\n", "    plt.ylabel('频数')\n", "    \n", "    plt.subplot(1, 2, 2)\n", "    plt.boxplot(df['unit_price'].dropna())\n", "    plt.title('房价箱线图')\n", "    plt.ylabel('单价 (元/平米)')\n", "    \n", "    plt.tight_layout()\n", "    plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 通勤时间与房价关系\n", "if 'commute_duration_min' in df.columns and 'unit_price' in df.columns:\n", "    plt.figure(figsize=(10, 6))\n", "    \n", "    # 散点图\n", "    valid_data = df[['commute_duration_min', 'unit_price']].dropna()\n", "    plt.scatter(valid_data['commute_duration_min'], valid_data['unit_price'], alpha=0.6)\n", "    \n", "    # 趋势线\n", "    z = np.polyfit(valid_data['commute_duration_min'], valid_data['unit_price'], 1)\n", "    p = np.poly1d(z)\n", "    plt.plot(valid_data['commute_duration_min'], p(valid_data['commute_duration_min']), \"r--\", alpha=0.8)\n", "    \n", "    plt.xlabel('通勤时间 (分钟)')\n", "    plt.ylabel('单价 (元/平米)')\n", "    plt.title('通勤时间与房价关系')\n", "    plt.grid(True, alpha=0.3)\n", "    \n", "    # 计算相关系数\n", "    correlation = valid_data['commute_duration_min'].corr(valid_data['unit_price'])\n", "    plt.text(0.05, 0.95, f'相关系数: {correlation:.3f}', transform=plt.gca().transAxes, \n", "             bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))\n", "    \n", "    plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. 特征工程示例"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 如果有完整数据，运行特征工程\n", "if 'convenience_index' in df.columns:\n", "    print(\"便利度指数统计:\")\n", "    print(df['convenience_index'].describe())\n", "    \n", "    # 便利度分布\n", "    plt.figure(figsize=(8, 5))\n", "    plt.hist(df['convenience_index'].dropna(), bins=20, alpha=0.7, color='green')\n", "    plt.title('生活便利度指数分布')\n", "    plt.xlabel('便利度指数')\n", "    plt.ylabel('频数')\n", "    plt.grid(True, alpha=0.3)\n", "    plt.show()\n", "else:\n", "    print(\"便利度指数数据不存在，需要运行特征工程\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. 相关性分析"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 选择数值列进行相关性分析\n", "numeric_columns = df.select_dtypes(include=[np.number]).columns\n", "if len(numeric_columns) > 1:\n", "    correlation_matrix = df[numeric_columns].corr()\n", "    \n", "    plt.figure(figsize=(10, 8))\n", "    sns.heatmap(correlation_matrix, annot=True, cmap='coolwarm', center=0, \n", "                square=True, fmt='.2f')\n", "    plt.title('特征相关性矩阵')\n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    # 显示与房价相关性最高的特征\n", "    if 'unit_price' in correlation_matrix.columns:\n", "        price_corr = correlation_matrix['unit_price'].abs().sort_values(ascending=False)\n", "        print(\"\\n与房价相关性最高的特征:\")\n", "        for feature, corr in price_corr.head(10).items():\n", "            if feature != 'unit_price':\n", "                print(f\"{feature}: {corr:.3f}\")\n", "else:\n", "    print(\"数值列不足，无法进行相关性分析\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8. 总结和下一步\n", "\n", "本notebook展示了项目的基本使用方法。要获得完整的分析结果，请：\n", "\n", "1. 配置有效的高德API密钥\n", "2. 运行完整的数据收集流程：`python src/main.py`\n", "3. 使用其他notebook进行深入分析\n", "\n", "### 项目特点：\n", "- ✅ 集成高德地图API获取准确的地理数据\n", "- ✅ 自动化的数据收集和处理流程\n", "- ✅ 多维度的特征工程\n", "- ✅ 可扩展的分析框架\n", "\n", "### 注意事项：\n", "- 🔑 需要有效的高德API密钥\n", "- ⏱️ 数据收集过程可能需要较长时间\n", "- 📊 建议在虚拟环境中运行"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.0"}}, "nbformat": 4, "nbformat_minor": 4}