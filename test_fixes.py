#!/usr/bin/env python3
"""
测试修复脚本
验证数据库连接、API调用和小区名清洗功能
"""

import logging
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.utils.logger_setup import setup_logger
from src.utils.config_loader import config

# 设置日志
setup_logger()
logger = logging.getLogger(__name__)

def test_database_connection():
    """测试数据库连接"""
    logger.info("=" * 50)
    logger.info("测试数据库连接")
    logger.info("=" * 50)
    
    try:
        from src.utils.database import db_manager
        
        # 尝试创建数据库
        db_manager.create_database_if_not_exists()
        logger.info("✓ 数据库创建成功")
        
        # 尝试创建表
        db_manager.create_tables()
        logger.info("✓ 数据表创建成功")
        
        # 测试连接
        if db_manager.engine:
            with db_manager.engine.connect() as conn:
                result = conn.execute("SELECT 1 as test")
                logger.info("✓ 数据库连接测试成功")
        
        return True
        
    except Exception as e:
        logger.error(f"✗ 数据库测试失败: {e}")
        return False

def test_api_connection():
    """测试高德API连接"""
    logger.info("=" * 50)
    logger.info("测试高德API连接")
    logger.info("=" * 50)
    
    try:
        from src.utils.api_helpers import geocode_address_gaode, clean_address_for_geocoding
        
        # 测试地址清洗
        test_addresses = [
            "上海市浦东新区陆家嘴",
            "梅园三街坊 官方核验新上近地铁精装",
            "星纪元 公寓拎包入住精装押一付一随时看房"
        ]
        
        logger.info("测试地址清洗功能:")
        for addr in test_addresses:
            cleaned = clean_address_for_geocoding(addr)
            logger.info(f"  原始: {addr}")
            logger.info(f"  清洗: {cleaned}")
        
        # 测试地理编码
        logger.info("测试地理编码功能:")
        test_coords = geocode_address_gaode("上海市浦东新区陆家嘴")
        if test_coords:
            logger.info(f"✓ 地理编码成功: {test_coords}")
            return True
        else:
            logger.warning("✗ 地理编码失败")
            return False
            
    except Exception as e:
        logger.error(f"✗ API测试失败: {e}")
        return False

def test_community_extraction():
    """测试小区名称提取"""
    logger.info("=" * 50)
    logger.info("测试小区名称提取")
    logger.info("=" * 50)
    
    try:
        from src.data_collection.community_poi_collector import CommunityPOICollector
        
        collector = CommunityPOICollector()
        
        # 测试小区名称清洗
        test_names = [
            "梅园三街坊",
            "星纪元公寓",
            "陆家嘴花园小区",
            "东方明珠大厦",
            "官方核验新上近地铁精装",
            "nan",
            "None"
        ]
        
        logger.info("测试小区名称清洗:")
        for name in test_names:
            cleaned = collector._clean_community_name(name)
            logger.info(f"  原始: {name} -> 清洗: {cleaned}")
        
        # 测试从标题提取小区名
        test_titles = [
            "梅园三街坊 官方核验新上近地铁精装",
            "星纪元 公寓拎包入住精装押一付一随时看房",
            "陆家嘴花园 2室1厅 精装修"
        ]
        
        logger.info("测试从标题提取小区名:")
        for title in test_titles:
            community = collector._extract_community_from_title(title)
            logger.info(f"  标题: {title}")
            logger.info(f"  小区: {community}")
        
        logger.info("✓ 小区名称提取测试完成")
        return True
        
    except Exception as e:
        logger.error(f"✗ 小区名称提取测试失败: {e}")
        return False

def test_poi_collection():
    """测试POI数据收集"""
    logger.info("=" * 50)
    logger.info("测试POI数据收集")
    logger.info("=" * 50)
    
    try:
        from src.utils.api_helpers import search_poi_around_gaode
        
        # 使用陆家嘴的坐标测试POI搜索
        coords_str = "121.499767,31.239638"  # 陆家嘴坐标
        
        logger.info(f"测试坐标: {coords_str}")
        
        # 测试餐饮POI搜索
        pois = search_poi_around_gaode(
            coords_str, 
            "050000",  # 餐饮服务
            radius=500,
            page_size=5,
            max_pages=1
        )
        
        if pois:
            logger.info(f"✓ POI搜索成功，找到 {len(pois)} 个餐饮POI")
            for i, poi in enumerate(pois[:3]):  # 显示前3个
                logger.info(f"  {i+1}. {poi.get('name', 'Unknown')} - {poi.get('address', 'No address')}")
            return True
        else:
            logger.warning("✗ POI搜索失败")
            return False
            
    except Exception as e:
        logger.error(f"✗ POI搜索测试失败: {e}")
        return False

def main():
    """主函数"""
    logger.info("开始测试修复效果...")
    
    results = {
        "数据库连接": test_database_connection(),
        "API连接": test_api_connection(),
        "小区名称提取": test_community_extraction(),
        "POI数据收集": test_poi_collection()
    }
    
    logger.info("=" * 50)
    logger.info("测试结果汇总")
    logger.info("=" * 50)
    
    all_passed = True
    for test_name, result in results.items():
        status = "✓ 通过" if result else "✗ 失败"
        logger.info(f"{test_name}: {status}")
        if not result:
            all_passed = False
    
    if all_passed:
        logger.info("🎉 所有测试通过！修复成功！")
    else:
        logger.warning("⚠️  部分测试失败，请检查相关配置")
    
    logger.info("=" * 50)
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
