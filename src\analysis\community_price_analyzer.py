"""
小区房价分析器
专注于计算小区的平均房价，只需要房价和面积数据
"""

import pandas as pd
import numpy as np
import logging
from typing import Optional
from ..utils.config_loader import config
from ..utils.file_io import load_dataframe_hybrid, save_dataframe_hybrid, get_data_path

logger = logging.getLogger(__name__)

class CommunityPriceAnalyzer:
    """小区房价分析器 - 简化版本，只关注房价和面积"""
    
    def __init__(self):
        """初始化分析器"""
        self.required_fields = ['community', 'total_price']  # 简化必需字段
        
    def analyze_community_prices(self, housing_file: str = None) -> str:
        """
        分析小区房价
        
        Args:
            housing_file: 房产数据文件路径
            
        Returns:
            输出文件路径
        """
        logger.info("=" * 50)
        logger.info("开始小区房价分析")
        logger.info("=" * 50)
        
        # 1. 加载房产数据
        if not housing_file:
            housing_file = config.get("PATHS", "processed_housing_data",
                                    "data/processed/housing_cleaned_geocoded.csv")
        
        housing_df = load_dataframe_hybrid(
            filepath=get_data_path(housing_file),
            table_name='housing_data_cleaned',
            prefer_db=False
        )
        
        if housing_df is None or len(housing_df) == 0:
            logger.error(f"无法加载房产数据: {housing_file}")
            return ""
        
        logger.info(f"加载房产数据: {len(housing_df)} 条记录")
        
        # 2. 数据预处理
        cleaned_df = self._preprocess_data(housing_df)
        if len(cleaned_df) == 0:
            logger.error("预处理后无有效数据")
            return ""
        
        # 3. 计算小区房价统计
        community_stats = self._calculate_community_price_stats(cleaned_df)
        if len(community_stats) == 0:
            logger.error("无法计算小区统计数据")
            return ""
        
        # 4. 保存结果
        output_file = config.get("PATHS", "community_price_analysis",
                                "data/analysis/community_price_stats.csv")
        output_path = self._save_results(community_stats, output_file)
        
        # 5. 输出统计摘要
        self._print_analysis_summary(community_stats)
        
        logger.info("=" * 50)
        logger.info("小区房价分析完成")
        logger.info("=" * 50)
        
        return output_path
    
    def _preprocess_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """预处理数据，只保留必要字段"""
        logger.info("预处理房产数据...")
        
        original_count = len(df)
        
        # 检查必要字段
        missing_fields = [field for field in self.required_fields if field not in df.columns]
        if missing_fields:
            logger.error(f"缺少必要字段: {missing_fields}")
            return pd.DataFrame()
        
        # 只保留必要字段
        essential_fields = ['community', 'total_price', 'unit_price', 'area', 'area_numeric']
        available_fields = [field for field in essential_fields if field in df.columns]
        df = df[available_fields].copy()

        logger.info(f"可用字段: {available_fields}")
        
        # 清洗小区名称
        df = self._clean_community_names(df)
        
        # 清洗价格和面积数据
        df = self._clean_price_area_data(df)
        
        # 过滤有效记录
        df = self._filter_valid_records(df)
        
        cleaned_count = len(df)
        logger.info(f"数据预处理完成: {original_count} → {cleaned_count} 条记录")
        
        return df
    
    def _clean_community_names(self, df: pd.DataFrame) -> pd.DataFrame:
        """清洗小区名称"""
        if 'community' not in df.columns:
            return df
        
        # 转换为字符串并去除空格
        df['community'] = df['community'].astype(str).str.strip()
        
        # 去除无效名称
        invalid_names = ['nan', 'None', '', '未知', '暂无', '其他', 'null']
        df = df[~df['community'].isin(invalid_names)]
        
        # 标准化小区名称
        import re
        df['community_clean'] = df['community'].str.replace(r'[（(].*?[）)]', '', regex=True)
        df['community_clean'] = df['community_clean'].str.replace(r'小区$', '', regex=True)
        df['community_clean'] = df['community_clean'].str.replace(r'公寓$', '', regex=True)
        df['community_clean'] = df['community_clean'].str.replace(r'花园$', '', regex=True)
        df['community_clean'] = df['community_clean'].str.strip()
        
        # 过滤太短的名称
        df = df[df['community_clean'].str.len() >= 2]
        
        logger.info(f"小区名称清洗完成，有效小区数: {df['community_clean'].nunique()}")
        
        return df
    
    def _clean_price_area_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """清洗价格和面积数据"""
        # 处理总价（万元）
        if 'total_price' in df.columns:
            df['total_price'] = pd.to_numeric(df['total_price'], errors='coerce')
            # 过滤异常值
            df.loc[df['total_price'] < 50, 'total_price'] = np.nan  # 低于50万
            df.loc[df['total_price'] > 10000, 'total_price'] = np.nan  # 高于1亿

        # 处理单价（元/平米）- 需要清洗中文字符
        if 'unit_price' in df.columns:
            # 清洗单价字段，提取数字
            import re
            df['unit_price_clean'] = df['unit_price'].astype(str).str.replace(',', '')  # 去除逗号
            df['unit_price_clean'] = df['unit_price_clean'].str.extract(r'(\d+)')[0]  # 提取数字
            df['unit_price_clean'] = pd.to_numeric(df['unit_price_clean'], errors='coerce')

            # 过滤异常值
            df.loc[df['unit_price_clean'] < 5000, 'unit_price_clean'] = np.nan  # 低于5000元/平米
            df.loc[df['unit_price_clean'] > 200000, 'unit_price_clean'] = np.nan  # 高于20万元/平米

            # 替换原字段
            df['unit_price'] = df['unit_price_clean']
            df.drop('unit_price_clean', axis=1, inplace=True)
        
        # 处理面积
        area_col = None
        if 'area_numeric' in df.columns:
            area_col = 'area_numeric'
        elif 'area' in df.columns:
            area_col = 'area'

        if area_col:
            # 清洗面积字段，提取数字
            import re
            df['area_clean'] = df[area_col].astype(str).str.extract(r'(\d+\.?\d*)')[0]  # 提取数字（包括小数）
            df['area_clean'] = pd.to_numeric(df['area_clean'], errors='coerce')

            # 过滤异常值
            df.loc[df['area_clean'] < 10, 'area_clean'] = np.nan  # 小于10平米
            df.loc[df['area_clean'] > 1000, 'area_clean'] = np.nan  # 大于1000平米
            logger.info(f"使用面积字段: {area_col}")
        else:
            logger.warning("未找到面积字段，将无法计算基于面积的统计")
        
        # 计算单价（如果缺失）
        if 'total_price' in df.columns and 'area_clean' in df.columns:
            # 从总价计算单价
            calculated_unit_price = (df['total_price'] * 10000) / df['area_clean']
            
            if 'unit_price' not in df.columns:
                df['unit_price'] = calculated_unit_price
            else:
                # 填补缺失的单价
                df['unit_price'] = df['unit_price'].fillna(calculated_unit_price)
        
        logger.info("价格和面积数据清洗完成")
        
        return df
    
    def _filter_valid_records(self, df: pd.DataFrame) -> pd.DataFrame:
        """过滤有效记录"""
        original_count = len(df)
        
        # 必须有小区名称
        df = df[df['community_clean'].notna()]
        
        # 必须有价格信息
        has_price = (df['total_price'].notna()) | (df['unit_price'].notna())
        df = df[has_price]
        
        # 必须有面积信息
        if 'area_clean' in df.columns:
            df = df[df['area_clean'].notna()]
        
        filtered_count = original_count - len(df)
        if filtered_count > 0:
            logger.info(f"过滤无效记录: {filtered_count} 条")
        
        return df
    
    def _calculate_community_price_stats(self, df: pd.DataFrame) -> pd.DataFrame:
        """计算小区房价统计"""
        logger.info("计算小区房价统计...")

        # 按小区分组
        community_groups = df.groupby('community_clean')

        stats_list = []
        filtered_communities = 0

        for community_name, group in community_groups:
            # 过滤数据量小于5的小区
            if len(group) < 5:
                filtered_communities += 1
                logger.debug(f"过滤小区 {community_name}：数据量不足 ({len(group)} < 5)")
                continue
            stats = {
                'community_name': community_name,
                'housing_count': len(group),
            }
            
            # 总价统计
            if 'total_price' in group.columns:
                total_prices = group['total_price'].dropna()
                if len(total_prices) > 0:
                    stats['avg_total_price'] = total_prices.mean()
                    stats['median_total_price'] = total_prices.median()
                    stats['min_total_price'] = total_prices.min()
                    stats['max_total_price'] = total_prices.max()
                    stats['total_price_count'] = len(total_prices)
            
            # 单价统计
            if 'unit_price' in group.columns:
                unit_prices = group['unit_price'].dropna()
                if len(unit_prices) > 0:
                    stats['avg_unit_price'] = unit_prices.mean()
                    stats['median_unit_price'] = unit_prices.median()
                    stats['min_unit_price'] = unit_prices.min()
                    stats['max_unit_price'] = unit_prices.max()
                    stats['unit_price_count'] = len(unit_prices)
            
            # 面积统计
            if 'area_clean' in group.columns:
                areas = group['area_clean'].dropna()
                if len(areas) > 0:
                    stats['avg_area'] = areas.mean()
                    stats['median_area'] = areas.median()
                    stats['min_area'] = areas.min()
                    stats['max_area'] = areas.max()
                    stats['area_count'] = len(areas)
            
            stats_list.append(stats)
        
        # 转换为DataFrame
        community_df = pd.DataFrame(stats_list)
        
        # 添加价格等级
        if 'avg_unit_price' in community_df.columns:
            community_df['price_level'] = pd.cut(
                community_df['avg_unit_price'],
                bins=[0, 30000, 50000, 80000, 120000, float('inf')],
                labels=['低价', '中低价', '中价', '中高价', '高价']
            )
        
        # 按平均单价排序
        if 'avg_unit_price' in community_df.columns:
            community_df = community_df.sort_values('avg_unit_price', ascending=False)

        # 输出过滤统计
        total_communities = len(community_groups)
        valid_communities = len(community_df)
        logger.info(f"小区过滤统计: 总计 {total_communities} 个小区，过滤 {filtered_communities} 个数据不足的小区")
        logger.info(f"小区房价统计计算完成，有效小区 {valid_communities} 个（数据量≥5）")

        return community_df
    
    def _save_results(self, df: pd.DataFrame, output_file: str) -> str:
        """保存分析结果"""
        output_path = get_data_path(output_file)
        
        success = save_dataframe_hybrid(
            df=df,
            filepath=output_path,
            table_name='community_price_stats',
            if_exists='replace'
        )
        
        if success:
            logger.info(f"分析结果已保存: {output_path}")
        else:
            logger.warning("保存过程中出现部分错误")
        
        return output_path
    
    def _print_analysis_summary(self, df: pd.DataFrame):
        """输出分析摘要"""
        logger.info("=" * 30)
        logger.info("小区房价分析摘要")
        logger.info("=" * 30)
        
        logger.info(f"分析小区数量: {len(df)}")
        
        if 'avg_unit_price' in df.columns:
            avg_prices = df['avg_unit_price'].dropna()
            if len(avg_prices) > 0:
                logger.info(f"平均单价范围: {avg_prices.min():.0f} - {avg_prices.max():.0f} 元/平米")
                logger.info(f"整体平均单价: {avg_prices.mean():.0f} 元/平米")
                
                # 显示前5个最贵的小区
                top5 = df.nlargest(5, 'avg_unit_price')[['community_name', 'avg_unit_price', 'housing_count']]
                logger.info("最贵的5个小区:")
                for _, row in top5.iterrows():
                    logger.info(f"  {row['community_name']}: {row['avg_unit_price']:.0f} 元/平米 ({row['housing_count']} 套)")
        
        if 'price_level' in df.columns:
            price_dist = df['price_level'].value_counts()
            logger.info("价格等级分布:")
            for level, count in price_dist.items():
                logger.info(f"  {level}: {count} 个小区")
        
        logger.info("=" * 30)
