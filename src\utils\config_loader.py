"""
配置文件加载器
负责加载和管理项目配置
"""

import configparser
import os
import logging
from pathlib import Path

logger = logging.getLogger(__name__)

class ConfigLoader:
    """配置加载器类"""
    
    def __init__(self, config_path=None):
        """
        初始化配置加载器
        
        Args:
            config_path (str): 配置文件路径，默认为 config/config.ini
        """
        if config_path is None:
            # 获取项目根目录
            project_root = Path(__file__).parent.parent.parent
            config_path = project_root / "config" / "config.ini"
        
        self.config_path = Path(config_path)
        self.config = configparser.ConfigParser()
        self.load_config()
    
    def load_config(self):
        """加载配置文件"""
        if not self.config_path.exists():
            logger.error(f"配置文件不存在: {self.config_path}")
            logger.info("请复制 config.ini.template 为 config.ini 并填入正确的配置")
            raise FileNotFoundError(f"配置文件不存在: {self.config_path}")
        
        try:
            self.config.read(self.config_path, encoding='utf-8')
            logger.info(f"成功加载配置文件: {self.config_path}")
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            raise
    
    def get(self, section, key, fallback=None):
        """
        获取配置值
        
        Args:
            section (str): 配置节
            key (str): 配置键
            fallback: 默认值
            
        Returns:
            str: 配置值
        """
        try:
            return self.config.get(section, key, fallback=fallback)
        except (configparser.NoSectionError, configparser.NoOptionError) as e:
            logger.warning(f"配置项不存在: [{section}] {key}, 使用默认值: {fallback}")
            return fallback
    
    def getint(self, section, key, fallback=None):
        """获取整数配置值"""
        try:
            return self.config.getint(section, key, fallback=fallback)
        except (configparser.NoSectionError, configparser.NoOptionError, ValueError) as e:
            logger.warning(f"配置项不存在或格式错误: [{section}] {key}, 使用默认值: {fallback}")
            return fallback
    
    def getfloat(self, section, key, fallback=None):
        """获取浮点数配置值"""
        try:
            return self.config.getfloat(section, key, fallback=fallback)
        except (configparser.NoSectionError, configparser.NoOptionError, ValueError) as e:
            logger.warning(f"配置项不存在或格式错误: [{section}] {key}, 使用默认值: {fallback}")
            return fallback
    
    def getboolean(self, section, key, fallback=None):
        """获取布尔配置值"""
        try:
            return self.config.getboolean(section, key, fallback=fallback)
        except (configparser.NoSectionError, configparser.NoOptionError, ValueError) as e:
            logger.warning(f"配置项不存在或格式错误: [{section}] {key}, 使用默认值: {fallback}")
            return fallback
    
    def get_section(self, section):
        """获取整个配置节"""
        try:
            return dict(self.config[section])
        except KeyError:
            logger.warning(f"配置节不存在: {section}")
            return {}

# 全局配置实例
config = ConfigLoader()
