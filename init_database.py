#!/usr/bin/env python3
"""
数据库初始化脚本
创建MySQL数据库和表结构
"""

import logging
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.utils.logger_setup import setup_logger
from src.utils.database import db_manager

# 设置日志
setup_logger()
logger = logging.getLogger(__name__)

def main():
    """主函数"""
    try:
        logger.info("开始初始化数据库...")

        # 首先尝试创建数据库
        try:
            db_manager.create_database_if_not_exists()
            logger.info("数据库创建或确认存在")
        except Exception as e:
            logger.error(f"创建数据库失败: {e}")
            # 继续尝试，可能数据库已存在

        # 创建数据库和表
        db_manager.create_tables()

        # 检查表是否创建成功
        tables = ['housing_data', 'poi_data', 'poi_aggregated', 'commute_data', 'community_aggregated', 'community_poi_data']

        for table in tables:
            if db_manager.table_exists(table):
                info = db_manager.get_table_info(table)
                logger.info(f"表 {table} 创建成功，当前行数: {info.get('row_count', 0)}")
            else:
                logger.warning(f"表 {table} 创建失败")

        logger.info("数据库初始化完成！")

    except Exception as e:
        logger.error(f"数据库初始化失败: {e}")
        # 不要直接退出，让用户知道问题
        logger.info("请检查MySQL服务是否运行，以及配置文件中的数据库连接信息是否正确")
        logger.info("如果MySQL未安装，请先安装MySQL服务器")
        sys.exit(1)

if __name__ == "__main__":
    main()
