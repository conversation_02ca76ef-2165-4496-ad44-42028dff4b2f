"""
日志设置模块
"""

import logging
import os
from pathlib import Path
from .config_loader import config

def setup_logger():
    """设置日志配置"""
    
    # 获取日志配置
    log_level = config.get("LOGGING", "log_level", "INFO")
    log_file = config.get("LOGGING", "log_file", "logs/cbd_analysis.log")
    log_format = config.get("LOGGING", "log_format", 
                           "%(asctime)s - %(name)s - %(levelname)s - %(message)s")
    
    # 创建日志目录
    log_path = Path(log_file)
    log_path.parent.mkdir(parents=True, exist_ok=True)
    
    # 设置日志级别
    level = getattr(logging, log_level.upper(), logging.INFO)
    
    # 配置日志
    logging.basicConfig(
        level=level,
        format=log_format,
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    
    # 设置第三方库日志级别
    logging.getLogger('urllib3').setLevel(logging.WARNING)
    logging.getLogger('requests').setLevel(logging.WARNING)
    
    logger = logging.getLogger(__name__)
    logger.info(f"日志系统初始化完成，级别: {log_level}")
    
    return logger
