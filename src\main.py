"""
主控脚本 - 优化版本
协调整个数据收集、处理和分析流程
增加重试机制、API限流控制和错误处理
"""

import logging
import sys
import os
import time
import pandas as pd
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.utils.config_loader import config
from src.utils.file_io import get_data_path
from src.data_collection.selenium_housing_scraper import SeleniumHousingScraper
from src.data_collection.community_poi_collector import CommunityPOICollector
from src.data_collection.commute_calculator import CommuteCalculator
from src.data_processing.clean_housing_data import HousingDataCleaner
from src.data_processing.clean_poi_data import POIDataCleaner
from src.data_processing.clean_commute_data import CommuteDataCleaner
from src.data_processing.merge_data import DataMerger
from src.feature_engineering.create_features import FeatureEngineer

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('cbd_analysis.log', encoding='utf-8')
    ]
)

logger = logging.getLogger(__name__)

def setup_directories():
    """创建必要的目录"""
    directories = [
        'data/raw',
        'data/processed', 
        'data/final',
        'outputs',
        'logs'
    ]
    
    for directory in directories:
        path = get_data_path(directory)
        os.makedirs(path, exist_ok=True)
    
    logger.info("目录结构创建完成")

def check_config():
    """检查配置文件"""
    try:
        # 检查API密钥
        api_key = config.get("API_KEYS", "gaode_ak")
        if not api_key or api_key == "YOUR_GAODE_API_KEY_HERE":
            logger.error("请在 config/config.ini 中配置有效的高德API密钥")
            return False

        logger.info("配置检查通过")
        return True

    except Exception as e:
        logger.error(f"配置检查失败: {e}")
        return False

def test_api_connectivity():
    """测试API连接"""
    logger.info("测试API连接...")

    try:
        from src.utils.api_helpers import geocode_address_gaode

        # 测试简单的地理编码
        coords = geocode_address_gaode("上海市浦东新区")
        if coords:
            logger.info(f"API连接正常，测试坐标: {coords}")
            return True
        else:
            logger.warning("API连接测试失败")
            return False

    except Exception as e:
        logger.error(f"API连接测试异常: {e}")
        return False

def run_housing_cleaning_with_retry(housing_file=None):
    """运行房产数据清洗（带重试机制）"""
    logger.info("=" * 50)
    logger.info("开始房产数据清洗")
    logger.info("=" * 50)

    max_retries = 3

    for attempt in range(max_retries):
        try:
            logger.info(f"清洗尝试 {attempt + 1}/{max_retries}")

            cleaner = HousingDataCleaner()
            # 如果提供了具体的房产文件，使用该文件
            if housing_file:
                cleaned_file = cleaner.clean_housing_data(input_file=housing_file)
            else:
                cleaned_file = cleaner.clean_housing_data()

            if cleaned_file and os.path.exists(cleaned_file):
                logger.info(f"房产数据清洗完成: {cleaned_file}")
                return cleaned_file
            else:
                logger.warning(f"清洗尝试 {attempt + 1} 失败")

        except Exception as e:
            logger.error(f"清洗尝试 {attempt + 1} 异常: {e}")

            if attempt < max_retries - 1:
                wait_time = (attempt + 1) * 30  # 递增等待时间
                logger.info(f"等待 {wait_time} 秒后重试...")
                time.sleep(wait_time)

    logger.error("房产数据清洗失败，已达最大重试次数")
    return None

def run_poi_collection_with_limit():
    """运行POI数据收集（限制数量）"""
    logger.info("=" * 50)
    logger.info("开始POI数据收集")
    logger.info("=" * 50)

    try:
        # 检查房产数据是否存在
        housing_file = config.get("PATHS", "processed_housing_data",
                                "data/processed/housing_cleaned_geocoded.csv")

        if not os.path.exists(get_data_path(housing_file)):
            logger.error("房产数据不存在，跳过POI收集")
            return None

        # 限制处理的房产数量以避免API超限
        import pandas as pd
        housing_df = pd.read_csv(get_data_path(housing_file))

        # 只处理有坐标的前50条记录（适度限制）
        valid_coords = housing_df[['longitude', 'latitude']].notna().all(axis=1)
        limited_df = housing_df[valid_coords].head(50)

        if len(limited_df) == 0:
            logger.warning("没有有效坐标的房产数据，跳过POI收集")
            return None

        logger.info(f"限制处理 {len(limited_df)} 条房产数据")

        # 保存限制后的数据
        limited_file = get_data_path("data/processed/housing_limited.csv")
        limited_df.to_csv(limited_file, index=False, encoding='utf-8-sig')

        # 收集POI数据
        poi_collector = CommunityPOICollector()
        poi_file = poi_collector.collect_community_poi_data(limited_file)

        logger.info(f"POI数据收集完成: {poi_file}")
        return poi_file

    except Exception as e:
        logger.error(f"POI数据收集失败: {e}")
        return None

def run_commute_calculation_with_limit():
    """运行通勤计算（限制数量）"""
    logger.info("=" * 50)
    logger.info("开始通勤数据计算")
    logger.info("=" * 50)

    try:
        # 使用限制后的房产数据
        limited_file = "data/processed/housing_limited.csv"

        if not os.path.exists(get_data_path(limited_file)):
            logger.error("限制房产数据不存在，跳过通勤计算")
            return None

        commute_calculator = CommuteCalculator()
        commute_file = commute_calculator.calculate_commute_times(limited_file)

        logger.info(f"通勤数据计算完成: {commute_file}")
        return commute_file

    except Exception as e:
        logger.error(f"通勤数据计算失败: {e}")
        return None

def run_data_collection(skip_housing=False, skip_poi=False, skip_commute=False):
    """运行数据收集流程"""
    logger.info("=" * 50)
    logger.info("开始数据收集阶段")
    logger.info("=" * 50)

    housing_file = None

    # 1. 房产数据爬取
    if not skip_housing:
        logger.info("步骤 1: 爬取房产数据")
        try:
            # 使用selenium爬虫，非无头模式以便手动登录
            scraper = SeleniumHousingScraper(headless=False)
            with scraper:
                housing_file = scraper.run_scraping()
            logger.info(f"房产数据爬取完成: {housing_file}")
        except Exception as e:
            logger.error(f"房产数据爬取失败: {e}")
            return False
    else:
        logger.info("跳过房产数据爬取")
        # 如果跳过爬取，查找最新的房产数据文件
        existing_data = check_existing_data()
        if existing_data and 'raw_housing' in existing_data:
            housing_file = existing_data['raw_housing']
            logger.info(f"使用已有房产数据: {housing_file}")

    # 2. 房产数据清洗（带重试机制）
    logger.info("步骤 2: 清洗房产数据")
    cleaned_housing_file = run_housing_cleaning_with_retry(housing_file)
    if not cleaned_housing_file:
        logger.error("房产数据清洗失败，程序退出")
        return False
    
    # 3. POI数据收集（限制数量）
    if not skip_poi:
        logger.info("步骤 3: 收集POI数据")
        poi_file = run_poi_collection_with_limit()
        if not poi_file:
            logger.warning("POI数据收集失败，但继续执行")
    else:
        logger.info("跳过POI数据收集")

    # 4. 通勤数据计算（限制数量）
    if not skip_commute:
        logger.info("步骤 4: 计算通勤数据")
        commute_file = run_commute_calculation_with_limit()
        if not commute_file:
            logger.warning("通勤数据计算失败，但继续执行")
    else:
        logger.info("跳过通勤数据计算")

    logger.info("数据收集阶段完成")
    return True

def run_data_processing():
    """运行数据处理流程"""
    logger.info("=" * 50)
    logger.info("开始数据处理阶段")
    logger.info("=" * 50)
    
    # 1. POI数据清洗
    logger.info("步骤 1: 清洗POI数据")
    try:
        poi_cleaner = POIDataCleaner()
        cleaned_poi_file = poi_cleaner.clean_poi_data()
        logger.info(f"POI数据清洗完成: {cleaned_poi_file}")
    except Exception as e:
        logger.error(f"POI数据清洗失败: {e}")
        return False
    
    # 2. 通勤数据清洗
    logger.info("步骤 2: 清洗通勤数据")
    try:
        commute_cleaner = CommuteDataCleaner()
        cleaned_commute_file = commute_cleaner.clean_commute_data()
        logger.info(f"通勤数据清洗完成: {cleaned_commute_file}")
    except Exception as e:
        logger.error(f"通勤数据清洗失败: {e}")
        return False
    
    # 3. 数据合并
    logger.info("步骤 3: 合并所有数据")
    try:
        merger = DataMerger()
        final_file = merger.merge_all_data()
        logger.info(f"数据合并完成: {final_file}")
    except Exception as e:
        logger.error(f"数据合并失败: {e}")
        return False
    
    # 4. 特征工程
    logger.info("步骤 4: 特征工程")
    try:
        feature_engineer = FeatureEngineer()
        final_file = feature_engineer.create_features()
        logger.info(f"特征工程完成: {final_file}")
    except Exception as e:
        logger.error(f"特征工程失败: {e}")
        return False

    logger.info("数据处理阶段完成")
    return True

def show_results():
    """显示结果"""
    logger.info("=" * 50)
    logger.info("分析结果")
    logger.info("=" * 50)

    try:
        import pandas as pd

        final_file = config.get("PATHS", "final_dataset", "data/final/final_merged_dataset.csv")
        final_path = get_data_path(final_file)

        if os.path.exists(final_path):
            df = pd.read_csv(final_path)

            logger.info(f"最终数据集: {len(df)} 条记录, {len(df.columns)} 个特征")

            # 显示关键统计
            if 'unit_price' in df.columns:
                logger.info(f"平均房价: {df['unit_price'].mean():.0f} 元/平米")
                logger.info(f"房价范围: {df['unit_price'].min():.0f} - {df['unit_price'].max():.0f} 元/平米")

            if 'convenience_index' in df.columns:
                logger.info(f"平均便利度指数: {df['convenience_index'].mean():.1f}")
                logger.info(f"便利度范围: {df['convenience_index'].min():.1f} - {df['convenience_index'].max():.1f}")

            if 'commute_duration_min' in df.columns:
                logger.info(f"平均通勤时间: {df['commute_duration_min'].mean():.1f} 分钟")
                logger.info(f"通勤时间范围: {df['commute_duration_min'].min():.1f} - {df['commute_duration_min'].max():.1f} 分钟")

            if 'livability_index' in df.columns:
                logger.info(f"平均宜居指数: {df['livability_index'].mean():.1f}")

            # 相关性分析
            if all(col in df.columns for col in ['unit_price', 'convenience_index']):
                price_convenience_corr = df['unit_price'].corr(df['convenience_index'])
                logger.info(f"房价与便利度相关性: {price_convenience_corr:.3f}")

            if all(col in df.columns for col in ['unit_price', 'commute_duration_min']):
                price_commute_corr = df['unit_price'].corr(df['commute_duration_min'])
                logger.info(f"房价与通勤时间相关性: {price_commute_corr:.3f}")

            logger.info(f"数据文件位置: {final_path}")

        else:
            logger.warning("最终数据集不存在")

    except Exception as e:
        logger.error(f"显示结果失败: {e}")

def initialize_database():
    """初始化数据库"""
    logger.info("初始化数据库...")
    try:
        from src.utils.database import db_manager

        # 创建数据库（如果不存在）
        db_manager.create_database_if_not_exists()

        # 创建表结构
        db_manager.create_tables()

        logger.info("数据库初始化完成")
        return True

    except Exception as e:
        logger.error(f"数据库初始化失败: {e}")
        logger.warning("将继续执行，但数据可能无法保存到数据库")
        return False

def check_existing_data():
    """检查已存在的数据文件，实现断点重连"""
    logger.info("检查已存在的数据文件...")

    # 检查已清洗的房产数据
    cleaned_housing_file = config.get("PATHS", "processed_housing_data",
                                    "data/processed/housing_cleaned_geocoded.csv")
    cleaned_housing_path = get_data_path(cleaned_housing_file)

    if os.path.exists(cleaned_housing_path):
        try:
            df = pd.read_csv(cleaned_housing_path, encoding='utf-8-sig')
            if len(df) > 0 and 'longitude' in df.columns and 'latitude' in df.columns:
                valid_coords = df[['longitude', 'latitude']].notna().all(axis=1).sum()
                if valid_coords > 0:
                    logger.info(f"✓ 发现已清洗的房产数据: {len(df)} 条记录，{valid_coords} 条有坐标")
                    return {
                        'cleaned_housing': cleaned_housing_path,
                        'housing_count': len(df),
                        'geocoded_count': valid_coords
                    }
        except Exception as e:
            logger.warning(f"读取已清洗房产数据失败: {e}")

    # 检查原始房产数据
    raw_data_dir = get_data_path("data/raw")
    if os.path.exists(raw_data_dir):
        housing_files = []
        for file in os.listdir(raw_data_dir):
            if file.startswith("lianjia_housing_selenium_") and file.endswith(".csv"):
                filepath = os.path.join(raw_data_dir, file)
                housing_files.append((filepath, os.path.getmtime(filepath)))

        if housing_files:
            # 获取最新文件
            housing_files.sort(key=lambda x: x[1], reverse=True)
            latest_file = housing_files[0][0]
            try:
                df = pd.read_csv(latest_file, encoding='utf-8-sig')
                if len(df) > 0:
                    logger.info(f"✓ 发现原始房产数据: {len(df)} 条记录")
                    return {
                        'raw_housing': latest_file,
                        'housing_count': len(df)
                    }
            except Exception as e:
                logger.warning(f"读取原始房产数据失败: {e}")

    logger.info("未发现可用的已有数据")
    return None

def main():
    """主函数"""
    logger.info("上海陆家嘴CBD周边住宅租售价格与多维度关联度研究 - 优化版本")
    logger.info("=" * 60)

    # 设置目录
    setup_directories()

    # 检查配置
    if not check_config():
        logger.error("配置检查失败，程序退出")
        return

    # 初始化数据库
    initialize_database()

    # 检查已有数据，实现断点重连
    existing_data = check_existing_data()

    if existing_data and 'cleaned_housing' in existing_data:
        logger.info("=" * 50)
        logger.info("发现已完成的地理编码数据，跳过数据收集阶段")
        logger.info("=" * 50)

        try:
            # 直接进入数据处理阶段
            success = run_data_processing()

            if not success:
                logger.error("数据处理阶段失败，程序退出")
                return

            # 显示结果
            show_results()

            logger.info("=" * 60)
            logger.info("优化版本执行完成！")
            logger.info("=" * 60)
            return

        except KeyboardInterrupt:
            logger.info("用户中断程序执行")
            return
        except Exception as e:
            logger.error(f"程序执行出错: {e}")
            raise

    # 测试API连接
    if not test_api_connectivity():
        logger.warning("API连接测试失败，但继续执行")

    try:
        # 数据收集阶段
        success = run_data_collection(
            skip_housing=(existing_data and 'raw_housing' in existing_data),  # 如果有原始数据就跳过爬取
            skip_poi=False,      # 设置为True可跳过POI数据收集
            skip_commute=False   # 设置为True可跳过通勤数据计算
        )

        if not success:
            logger.error("数据收集阶段失败，程序退出")
            return

        # 数据处理阶段
        success = run_data_processing()

        if not success:
            logger.error("数据处理阶段失败，程序退出")
            return

        # 显示结果
        show_results()

        logger.info("=" * 60)
        logger.info("优化版本执行完成！")
        logger.info("=" * 60)

    except KeyboardInterrupt:
        logger.info("用户中断程序执行")
    except Exception as e:
        logger.error(f"程序执行出错: {e}")
        raise

if __name__ == "__main__":
    main()
