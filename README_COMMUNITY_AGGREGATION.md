# 小区房源数据聚合分析系统

## 概述

本系统实现了对爬虫获取的房源数据按小区进行归类，计算每个小区的每平方米均价，并对各个小区进行POI指数计算的功能。

## 主要功能

### 1. 小区房源数据聚合
- **按小区归类**: 将房源数据按所属小区进行分组
- **均价计算**: 计算每个小区的每平方米均价
- **统计信息**: 提供房源数量、面积分布、价格分布等统计信息

### 2. 小区POI便利度分析
- **POI数据聚合**: 将房源级别的POI数据聚合到小区级别
- **便利度指数**: 计算小区的综合便利度指数
- **分类评分**: 按餐饮、购物、医疗、教育、休闲、交通、金融等类别评分

### 3. 综合分析
- **多维度评价**: 结合房价、便利度、距离等因素
- **排名系统**: 提供小区综合评价排名
- **可视化展示**: 清晰展示分析结果

## 核心模块

### 1. HousingDataCleaner (src/data_processing/clean_housing_data.py)
**新增功能**:
- `aggregate_by_community()`: 按小区聚合房源数据
- `_clean_community_names()`: 清理和标准化小区名称
- `_calculate_community_statistics()`: 计算小区级别统计信息
- `_extract_community_info()`: 从房源信息中提取小区名称

**主要特性**:
- 支持从`house_info`字段自动提取小区信息
- 智能处理租房和二手房数据
- 计算小区平均单价、租金、面积等指标
- 自动计算到CBD的距离

### 2. CommunityPOIAggregator (src/data_processing/community_poi_aggregator.py)
**功能**:
- 将房源级别的POI数据聚合到小区级别
- 计算小区POI便利度指数
- 支持多种POI类别的加权评分

**评分体系**:
- 餐饮 (20%)
- 购物 (15%)
- 医疗 (15%)
- 教育 (10%)
- 休闲 (10%)
- 交通 (25%) - 权重最高
- 金融 (5%)

### 3. 主程序 (run_community_housing_analysis.py)
**功能**:
- 完整的小区分析流程
- 支持分步骤执行
- 提供详细的分析报告

## 使用方法

### 1. 基本使用
```bash
# 运行完整分析流程
python run_community_housing_analysis.py

# 只执行小区聚合
python run_community_housing_analysis.py --step aggregate

# 只执行POI聚合
python run_community_housing_analysis.py --step poi

# 只执行综合分析
python run_community_housing_analysis.py --step analysis
```

### 2. 指定数据文件
```bash
# 指定房源数据文件
python run_community_housing_analysis.py --housing-file data/raw/my_housing_data.csv

# 指定POI数据文件
python run_community_housing_analysis.py --poi-file data/processed/my_poi_data.csv

# 显示前N个小区
python run_community_housing_analysis.py --top-n 15
```

### 3. 测试功能
```bash
# 运行简化测试
python test_community_analysis_simple.py

# 运行完整测试
python test_community_analysis.py
```

## 数据流程

### 输入数据
1. **房源数据**: 包含标题、小区、价格、面积、坐标等信息
2. **POI数据**: 包含各类POI的数量、评分、距离等信息

### 处理步骤
1. **数据清洗**: 提取小区信息，标准化数据格式
2. **小区聚合**: 按小区分组，计算统计指标
3. **POI聚合**: 聚合POI数据，计算便利度指数
4. **综合分析**: 合并数据，计算综合评分

### 输出结果
1. **小区房源统计**: `data/processed/community_aggregated_housing.csv`
2. **小区POI数据**: `data/processed/community_poi_aggregated.csv`
3. **综合分析结果**: `data/final/final_community_analysis.csv`

## 配置说明

### 文件路径配置 (config/config.ini)
```ini
[PATHS]
# 原始数据
raw_housing_data = data/raw/housing_listings_lujiazui.csv
processed_poi_data = data/processed/poi_gaode_aggregated.csv

# 小区聚合数据
community_aggregated_data = data/processed/community_aggregated_housing.csv
community_poi_data = data/processed/community_poi_aggregated.csv

# 最终结果
final_community_data = data/final/final_community_analysis.csv
```

### POI权重配置
可在`CommunityPOIAggregator`类中调整各类POI的权重:
```python
self.poi_weights = {
    'catering': 0.20,      # 餐饮
    'shopping': 0.15,      # 购物
    'medical': 0.15,       # 医疗
    'education': 0.10,     # 教育
    'leisure': 0.10,       # 休闲
    'transportation': 0.25, # 交通
    'finance': 0.05        # 金融
}
```

## 输出示例

### 小区聚合结果
```
📍 瀚龙苑:
   房源数量: 5 套
   平均租金: 10181 元/月
   平均面积: 100.6 平米
   距离CBD: 0.6 公里
```

### POI便利度分析
```
小区POI便利度TOP5:
  1. 瀚龙苑: 65.5 分
     catering: 11.0 个
     shopping: 7.5 个
     medical: 9.8 个
     ...
```

### 综合评价
```
🏆 综合评价TOP5小区:

1. 瀚龙苑
   综合得分: 45.9
   便利度指数: 65.5
   距离CBD: 0.6 公里
```

## 技术特点

### 1. 数据兼容性
- 支持二手房和租房数据
- 自动识别和提取小区信息
- 智能处理缺失数据

### 2. 灵活配置
- 可调整POI权重
- 支持自定义评分标准
- 模块化设计，易于扩展

### 3. 错误处理
- 完善的异常处理机制
- 详细的日志记录
- 数据质量检查

## 注意事项

1. **数据格式**: 确保房源数据包含必要的字段（标题、小区、价格、面积等）
2. **坐标信息**: 如果缺少坐标，系统会尝试进行地理编码
3. **API限制**: 地理编码功能依赖高德API，注意API调用限制
4. **内存使用**: 大数据集处理时注意内存使用情况

## 扩展建议

1. **增加更多POI类别**: 如公园、学校等级评估
2. **时间序列分析**: 跟踪小区价格变化趋势
3. **可视化功能**: 添加地图展示和图表分析
4. **机器学习**: 使用ML模型预测房价和便利度

## 联系方式

如有问题或建议，请通过以下方式联系：
- 查看项目文档
- 提交Issue
- 参考测试用例
