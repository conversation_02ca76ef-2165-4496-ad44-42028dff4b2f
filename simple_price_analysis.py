#!/usr/bin/env python3
"""
简化的小区房价分析程序
只关注房价和面积数据，计算小区平均房价
"""

import logging
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.utils.logger_setup import setup_logger
from src.utils.config_loader import config
from src.utils.file_io import get_data_path
from src.analysis.community_price_analyzer import CommunityPriceAnalyzer

# 设置日志
setup_logger()
logger = logging.getLogger(__name__)

def check_existing_housing_data():
    """检查已有的房产数据"""
    logger.info("检查已有房产数据...")

    # 优先检查原始房产数据（因为处理后的数据可能是测试数据）
    raw_data_dir = get_data_path("data/raw")
    if os.path.exists(raw_data_dir):
        housing_files = []
        for file in os.listdir(raw_data_dir):
            if file.startswith("lianjia_housing_selenium_") and file.endswith(".csv"):
                filepath = os.path.join(raw_data_dir, file)
                housing_files.append((filepath, os.path.getmtime(filepath)))

        if housing_files:
            # 获取最新文件
            housing_files.sort(key=lambda x: x[1], reverse=True)
            latest_file = housing_files[0][0]
            try:
                import pandas as pd
                df = pd.read_csv(latest_file, encoding='utf-8-sig')
                if len(df) > 0:
                    logger.info(f"✓ 发现原始房产数据: {len(df)} 条记录")

                    # 检查小区分布
                    if 'community' in df.columns:
                        community_counts = df['community'].value_counts()
                        valid_communities = community_counts[community_counts >= 5]
                        logger.info(f"总小区数: {df['community'].nunique()}, 有效小区数（≥5条数据）: {len(valid_communities)}")

                        if len(valid_communities) > 0:
                            return latest_file
                        else:
                            logger.warning("原始数据中没有数据量≥5的小区")
                    else:
                        logger.warning("原始数据缺少community字段")
            except Exception as e:
                logger.warning(f"读取原始房产数据失败: {e}")

    # 检查已清洗的房产数据
    cleaned_housing_file = config.get("PATHS", "processed_housing_data",
                                    "data/processed/housing_cleaned_geocoded.csv")
    cleaned_housing_path = get_data_path(cleaned_housing_file)

    if os.path.exists(cleaned_housing_path):
        try:
            import pandas as pd
            df = pd.read_csv(cleaned_housing_path, encoding='utf-8-sig')
            if len(df) > 0:
                logger.info(f"✓ 发现已清洗的房产数据: {len(df)} 条记录")

                # 检查小区分布
                if 'community' in df.columns:
                    community_counts = df['community'].value_counts()
                    valid_communities = community_counts[community_counts >= 5]
                    logger.info(f"总小区数: {df['community'].nunique()}, 有效小区数（≥5条数据）: {len(valid_communities)}")

                    if len(valid_communities) > 0:
                        return cleaned_housing_path
                    else:
                        logger.warning("已清洗数据中没有数据量≥5的小区，可能是测试数据")
                else:
                    logger.warning("已清洗数据缺少community字段")
        except Exception as e:
            logger.warning(f"读取已清洗房产数据失败: {e}")

    logger.error("未找到可用的房产数据文件")
    return None

def run_simple_price_analysis():
    """运行简化的房价分析"""
    logger.info("=" * 60)
    logger.info("小区房价分析程序 - 简化版")
    logger.info("只关注房价和面积数据，计算小区平均房价")
    logger.info("=" * 60)
    
    try:
        # 1. 检查数据文件
        housing_file = check_existing_housing_data()
        if not housing_file:
            logger.error("无可用的房产数据，程序退出")
            logger.info("请先运行爬虫获取房产数据")
            return False
        
        # 2. 运行房价分析
        logger.info("=" * 50)
        logger.info("开始房价分析")
        logger.info("=" * 50)
        
        analyzer = CommunityPriceAnalyzer()
        result_file = analyzer.analyze_community_prices(housing_file)
        
        if result_file:
            logger.info(f"✓ 房价分析完成: {result_file}")
            
            # 3. 显示结果文件位置
            logger.info("=" * 50)
            logger.info("分析完成")
            logger.info("=" * 50)
            logger.info(f"结果文件: {result_file}")
            logger.info("您可以打开CSV文件查看详细的小区房价统计")
            
            return True
        else:
            logger.error("房价分析失败")
            return False
            
    except Exception as e:
        logger.error(f"程序执行出错: {e}")
        return False

def show_usage():
    """显示使用说明"""
    print("""
小区房价分析程序 - 简化版

功能：
- 从房产数据中提取小区名称、房价和面积信息
- 计算每个小区的平均房价、中位数房价等统计指标
- 生成小区房价排行榜

使用方法：
1. 确保已有房产数据文件（爬虫获取的数据）
2. 运行: python simple_price_analysis.py
3. 查看生成的CSV文件获取分析结果

输出文件：
- data/analysis/community_price_stats.csv

包含字段：
- community_name: 小区名称
- housing_count: 房源数量
- avg_unit_price: 平均单价（元/平米）
- median_unit_price: 中位数单价
- avg_total_price: 平均总价（万元）
- price_level: 价格等级（低价/中低价/中价/中高价/高价）
""")

def main():
    """主函数"""
    if len(sys.argv) > 1 and sys.argv[1] in ['-h', '--help', 'help']:
        show_usage()
        return
    
    success = run_simple_price_analysis()
    
    if success:
        logger.info("🎉 程序执行成功！")
    else:
        logger.error("❌ 程序执行失败")
        sys.exit(1)

if __name__ == "__main__":
    main()
