#!/usr/bin/env python3
"""
分析脚本启动器
提供统一的入口来运行各种分析脚本
"""

import sys
import argparse
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='陆家嘴房产分析脚本启动器')
    parser.add_argument('analysis_type', 
                       choices=['main', 'community', 'housing-aggregate'],
                       help='选择分析类型')
    parser.add_argument('--args', nargs='*', default=[], 
                       help='传递给分析脚本的参数')
    
    args = parser.parse_args()
    
    if args.analysis_type == 'main':
        print("启动主程序分析...")
        from src.main import main as main_analysis
        main_analysis()
        
    elif args.analysis_type == 'community':
        print("启动小区综合分析...")
        import subprocess
        script_path = project_root / "scripts" / "community_analysis" / "run_community_analysis.py"
        cmd = [sys.executable, str(script_path)] + args.args
        subprocess.run(cmd)
        
    elif args.analysis_type == 'housing-aggregate':
        print("启动小区房源聚合分析...")
        import subprocess
        script_path = project_root / "scripts" / "community_analysis" / "run_community_housing_analysis.py"
        cmd = [sys.executable, str(script_path)] + args.args
        subprocess.run(cmd)
    
    print("分析完成！")

if __name__ == "__main__":
    main()
