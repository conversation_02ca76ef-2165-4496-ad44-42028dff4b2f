#!/usr/bin/env python3
"""
项目结构测试脚本
验证新的项目结构是否正常工作
"""

import sys
import os
from pathlib import Path

def test_project_structure():
    """测试项目结构"""
    print("=" * 60)
    print("项目结构测试")
    print("=" * 60)
    
    # 检查主要目录
    directories = [
        "src",
        "scripts",
        "scripts/community_analysis",
        "config",
        "data",
        "notebooks"
    ]
    
    print("\n📁 检查目录结构:")
    for directory in directories:
        if os.path.exists(directory):
            print(f"   ✅ {directory}")
        else:
            print(f"   ❌ {directory}")
    
    # 检查主要文件
    files = [
        "src/main.py",
        "scripts/run_analysis.py",
        "scripts/README.md",
        "scripts/community_analysis/run_community_analysis.py",
        "scripts/community_analysis/run_community_housing_analysis.py",
        "README.md"
    ]
    
    print("\n📄 检查关键文件:")
    for file in files:
        if os.path.exists(file):
            print(f"   ✅ {file}")
        else:
            print(f"   ❌ {file}")
    
    # 测试模块导入
    print("\n🔧 测试模块导入:")
    
    try:
        # 添加项目根目录到路径
        project_root = Path(__file__).parent
        sys.path.insert(0, str(project_root))
        
        # 测试主要模块
        from src.utils.config_loader import config
        print("   ✅ config_loader")
        
        from src.data_collection.community_collector import CommunityCollector
        print("   ✅ CommunityCollector")
        
        from src.data_collection.community_poi_collector import CommunityPOICollector
        print("   ✅ CommunityPOICollector")
        
        from src.data_collection.community_commute_calculator import CommunityCommuteCalculator
        print("   ✅ CommunityCommuteCalculator")
        
        from src.analysis.community_scoring_system import CommunityScoringSystem
        print("   ✅ CommunityScoringSystem")
        
    except ImportError as e:
        print(f"   ❌ 模块导入失败: {e}")
    
    # 测试脚本可执行性
    print("\n🚀 测试脚本可执行性:")
    
    scripts_to_test = [
        ("主程序", "python src/main.py --help"),
        ("小区分析", "python scripts/community_analysis/run_community_analysis.py --help"),
        ("房源聚合", "python scripts/community_analysis/run_community_housing_analysis.py --help"),
        ("统一启动器", "python scripts/run_analysis.py --help")
    ]
    
    for name, command in scripts_to_test:
        try:
            import subprocess
            result = subprocess.run(command.split(), capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                print(f"   ✅ {name}")
            else:
                print(f"   ❌ {name} (返回码: {result.returncode})")
        except Exception as e:
            print(f"   ❌ {name} (错误: {e})")
    
    print("\n" + "=" * 60)
    print("项目结构测试完成")
    print("=" * 60)

def show_usage_examples():
    """显示使用示例"""
    print("\n📖 使用示例:")
    print("\n1. 主程序分析（基于房源）:")
    print("   python src/main.py")
    print("   python scripts/run_analysis.py main")
    
    print("\n2. 小区综合分析:")
    print("   python scripts/run_analysis.py community")
    print("   python scripts/community_analysis/run_community_analysis.py")
    print("   python scripts/community_analysis/run_community_analysis.py --step community")
    
    print("\n3. 小区房源聚合分析:")
    print("   python scripts/run_analysis.py housing-aggregate")
    print("   python scripts/community_analysis/run_community_housing_analysis.py")
    
    print("\n4. 查看帮助:")
    print("   python scripts/run_analysis.py --help")
    print("   python scripts/community_analysis/run_community_analysis.py --help")

if __name__ == "__main__":
    test_project_structure()
    show_usage_examples()
