"""
POI数据收集器
使用高德API收集小区周边的POI数据
"""

import pandas as pd
import logging
from typing import List, Dict, Optional
from tqdm import tqdm
from ..utils.config_loader import config
from ..utils.api_helpers import search_poi_around_gaode, geocode_address_gaode
from ..utils.file_io import load_dataframe, save_json, get_data_path

logger = logging.getLogger(__name__)

class POICollector:
    """POI数据收集器"""
    
    def __init__(self):
        self.search_radius = config.getint("SCRAPER_PARAMS", "poi_search_radius_m", 1000)
        self.poi_types = self._load_poi_types()
        
    def _load_poi_types(self) -> Dict[str, str]:
        """加载POI类型配置 - 使用固定的高德POI分类编码"""
        # 高德POI分类编码 - 固定配置，不需要在config文件中修改
        poi_types = {
            'catering': '050000',           # 餐饮服务
            'shopping': '060000',           # 购物服务
            'medical': '090000',            # 医疗保健
            'education': '140000',          # 科教文化
            'leisure': '110000|070000|080000',  # 休闲娱乐|体育休闲|生活服务
            'transportation': '150500|150700',  # 地铁站|公交站
            'finance': '160000',            # 金融保险
            'government': '170000'          # 政府机构
        }

        logger.info(f"加载POI类型配置: {list(poi_types.keys())}")
        return poi_types
    
    def collect_housing_poi_data(self, housing_file: str = None) -> str:
        """
        收集房产周边POI数据
        
        Args:
            housing_file: 房产数据文件路径
            
        Returns:
            保存的POI数据文件路径
        """
        # 加载房产数据
        if not housing_file:
            housing_file = config.get("PATHS", "processed_housing_data",
                                    "data/processed/housing_cleaned_geocoded.csv")
        
        housing_df = load_dataframe(get_data_path(housing_file))
        if housing_df is None:
            logger.error(f"无法加载房产数据: {housing_file}")
            return ""
        
        logger.info(f"加载房产数据: {len(housing_df)} 条记录")
        
        # 检查是否有经纬度信息
        if 'longitude' not in housing_df.columns or 'latitude' not in housing_df.columns:
            logger.warning("房产数据缺少经纬度信息，尝试进行地理编码")
            housing_df = self._geocode_housing_data(housing_df)
        
        # 收集POI数据
        poi_data = []
        
        for idx, row in tqdm(housing_df.iterrows(), total=len(housing_df), desc="收集POI数据"):
            try:
                # 检查经纬度
                if pd.isna(row.get('longitude')) or pd.isna(row.get('latitude')):
                    logger.warning(f"跳过无坐标的房产: {row.get('title', 'Unknown')}")
                    continue
                
                # 构建坐标字符串
                coords_str = f"{row['longitude']},{row['latitude']}"
                
                # 收集各类POI
                housing_poi = {
                    'housing_id': idx,
                    'title': row.get('title', ''),
                    'community': row.get('community', ''),
                    'longitude': row['longitude'],
                    'latitude': row['latitude'],
                    'poi_data': {}
                }
                
                # 按类别收集POI
                for category, type_codes in self.poi_types.items():
                    logger.debug(f"收集 {category} POI数据")
                    
                    pois = search_poi_around_gaode(
                        coords_str, 
                        type_codes, 
                        radius=self.search_radius
                    )
                    
                    housing_poi['poi_data'][category] = pois
                    logger.debug(f"获取 {category} POI: {len(pois)} 条")
                
                poi_data.append(housing_poi)
                
            except Exception as e:
                logger.error(f"收集POI数据失败 (房产ID: {idx}): {e}")
                continue
        
        # 保存POI数据
        output_file = config.get("PATHS", "raw_poi_data", 
                                "data/raw/poi_gaode_around_housing.json")
        output_path = get_data_path(output_file)
        
        save_json(poi_data, output_path)
        logger.info(f"POI数据已保存: {output_path}")
        
        return output_path
    
    def _geocode_housing_data(self, housing_df: pd.DataFrame) -> pd.DataFrame:
        """
        对房产数据进行地理编码
        
        Args:
            housing_df: 房产数据DataFrame
            
        Returns:
            包含经纬度的DataFrame
        """
        logger.info("开始地理编码房产数据")
        
        # 准备地址列表
        addresses = []
        for _, row in housing_df.iterrows():
            # 构建完整地址
            address_parts = []
            
            if row.get('community'):
                address_parts.append(row['community'])
            if row.get('district'):
                address_parts.append(row['district'])
            if row.get('position_info'):
                address_parts.append(row['position_info'])
            
            address = ' '.join(address_parts) if address_parts else row.get('title', '')
            addresses.append(address)
        
        # 批量地理编码
        from ..utils.api_helpers import batch_geocode_addresses
        coords_list = batch_geocode_addresses(addresses, city="上海", delay=0.3)
        
        # 添加经纬度到DataFrame
        longitudes = []
        latitudes = []
        
        for coords in coords_list:
            if coords:
                longitudes.append(coords[0])
                latitudes.append(coords[1])
            else:
                longitudes.append(None)
                latitudes.append(None)
        
        housing_df['longitude'] = longitudes
        housing_df['latitude'] = latitudes
        
        # 统计成功率
        success_count = sum(1 for coords in coords_list if coords is not None)
        logger.info(f"地理编码完成，成功率: {success_count}/{len(addresses)} ({success_count/len(addresses)*100:.1f}%)")
        
        return housing_df
    
    def collect_poi_by_category(self, coords_str: str, category: str) -> List[Dict]:
        """
        按类别收集POI数据
        
        Args:
            coords_str: 坐标字符串 "lng,lat"
            category: POI类别
            
        Returns:
            POI列表
        """
        if category not in self.poi_types:
            logger.warning(f"未知的POI类别: {category}")
            return []
        
        type_codes = self.poi_types[category]
        return search_poi_around_gaode(coords_str, type_codes, radius=self.search_radius)
    
    def get_poi_summary(self, poi_data_file: str = None) -> Dict:
        """
        获取POI数据摘要
        
        Args:
            poi_data_file: POI数据文件路径
            
        Returns:
            数据摘要
        """
        if not poi_data_file:
            poi_data_file = config.get("PATHS", "raw_poi_data",
                                     "data/raw/poi_gaode_around_housing.json")
        
        from ..utils.file_io import load_json
        poi_data = load_json(get_data_path(poi_data_file))
        
        if not poi_data:
            return {}
        
        summary = {
            'total_housing': len(poi_data),
            'categories': {},
            'total_pois': 0
        }
        
        for housing in poi_data:
            poi_categories = housing.get('poi_data', {})
            
            for category, pois in poi_categories.items():
                if category not in summary['categories']:
                    summary['categories'][category] = {
                        'total_pois': 0,
                        'avg_per_housing': 0,
                        'max_per_housing': 0
                    }
                
                poi_count = len(pois)
                summary['categories'][category]['total_pois'] += poi_count
                summary['categories'][category]['max_per_housing'] = max(
                    summary['categories'][category]['max_per_housing'], 
                    poi_count
                )
                summary['total_pois'] += poi_count
        
        # 计算平均值
        for category in summary['categories']:
            summary['categories'][category]['avg_per_housing'] = (
                summary['categories'][category]['total_pois'] / summary['total_housing']
            )
        
        return summary
