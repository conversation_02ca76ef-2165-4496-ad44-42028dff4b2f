"""
Selenium基础类
提供通用的浏览器自动化功能
"""

import time
import logging
import random
from typing import Optional, List, Dict, Any
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException, WebDriverException
from webdriver_manager.chrome import ChromeDriverManager

logger = logging.getLogger(__name__)

class SeleniumBase:
    """Selenium基础类"""
    
    def __init__(self, headless: bool = False, delay: float = 2.0, timeout: int = 10):
        """
        初始化Selenium基础类
        
        Args:
            headless: 是否无头模式运行
            delay: 操作间隔（秒）
            timeout: 等待超时时间（秒）
        """
        self.headless = headless
        self.delay = delay
        self.timeout = timeout
        self.driver = None
        self.wait = None
        
    def setup_driver(self) -> bool:
        """
        设置Chrome WebDriver
        
        Returns:
            是否设置成功
        """
        try:
            # Chrome选项配置
            chrome_options = Options()
            
            if self.headless:
                chrome_options.add_argument('--headless')
            
            # 常用选项
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1920,1080')

            # 网络和SSL相关选项（解决SSL握手错误）
            chrome_options.add_argument('--ignore-ssl-errors')
            chrome_options.add_argument('--ignore-certificate-errors')
            chrome_options.add_argument('--ignore-certificate-errors-spki-list')
            chrome_options.add_argument('--disable-web-security')
            chrome_options.add_argument('--allow-running-insecure-content')
            chrome_options.add_argument('--disable-features=VizDisplayCompositor')

            # WebGL相关选项（解决WebGL警告）
            chrome_options.add_argument('--enable-unsafe-swiftshader')
            chrome_options.add_argument('--disable-software-rasterizer')
            chrome_options.add_argument('--disable-background-timer-throttling')
            chrome_options.add_argument('--disable-backgrounding-occluded-windows')
            chrome_options.add_argument('--disable-renderer-backgrounding')

            # 日志级别设置（减少错误信息输出）
            chrome_options.add_argument('--log-level=3')
            chrome_options.add_argument('--silent')
            chrome_options.add_experimental_option('excludeSwitches', ['enable-logging'])
            chrome_options.add_experimental_option('useAutomationExtension', False)

            # 反检测选项
            chrome_options.add_argument('--disable-blink-features=AutomationControlled')
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            
            # 用户代理
            user_agents = [
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
                'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
            ]
            chrome_options.add_argument(f'--user-agent={random.choice(user_agents)}')
            
            # 自动下载ChromeDriver
            service = Service(ChromeDriverManager().install())
            
            # 创建WebDriver实例
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            
            # 执行反检测脚本
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            # 创建WebDriverWait实例
            self.wait = WebDriverWait(self.driver, self.timeout)
            
            logger.info("Chrome WebDriver设置成功")
            return True
            
        except Exception as e:
            logger.error(f"WebDriver设置失败: {e}")
            return False
    
    def get_page(self, url: str) -> bool:
        """
        访问页面
        
        Args:
            url: 目标URL
            
        Returns:
            是否访问成功
        """
        try:
            logger.info(f"访问页面: {url}")
            self.driver.get(url)
            
            # 等待页面加载
            self.wait.until(EC.presence_of_element_located((By.TAG_NAME, "body")))
            
            # 添加随机延时
            time.sleep(random.uniform(self.delay, self.delay * 1.5))
            
            logger.debug(f"页面访问成功: {url}")
            return True
            
        except TimeoutException:
            logger.error(f"页面加载超时: {url}")
            return False
        except WebDriverException as e:
            logger.error(f"页面访问失败: {url}, 错误: {e}")
            return False
    
    def wait_for_element(self, by: By, value: str, timeout: Optional[int] = None) -> Optional[Any]:
        """
        等待元素出现
        
        Args:
            by: 定位方式
            value: 定位值
            timeout: 超时时间
            
        Returns:
            元素对象或None
        """
        try:
            wait_time = timeout or self.timeout
            element = WebDriverWait(self.driver, wait_time).until(
                EC.presence_of_element_located((by, value))
            )
            return element
        except TimeoutException:
            logger.debug(f"元素等待超时: {by}={value}")
            return None
    
    def find_elements_safe(self, by: By, value: str) -> List[Any]:
        """
        安全查找元素列表
        
        Args:
            by: 定位方式
            value: 定位值
            
        Returns:
            元素列表
        """
        try:
            return self.driver.find_elements(by, value)
        except NoSuchElementException:
            logger.debug(f"未找到元素: {by}={value}")
            return []
        except Exception as e:
            logger.error(f"查找元素失败: {by}={value}, 错误: {e}")
            return []
    
    def find_element_safe(self, by: By, value: str) -> Optional[Any]:
        """
        安全查找单个元素
        
        Args:
            by: 定位方式
            value: 定位值
            
        Returns:
            元素对象或None
        """
        try:
            return self.driver.find_element(by, value)
        except NoSuchElementException:
            logger.debug(f"未找到元素: {by}={value}")
            return None
        except Exception as e:
            logger.error(f"查找元素失败: {by}={value}, 错误: {e}")
            return None
    
    def scroll_to_bottom(self, pause_time: float = 1.0) -> None:
        """
        滚动到页面底部
        
        Args:
            pause_time: 滚动间隔时间
        """
        try:
            last_height = self.driver.execute_script("return document.body.scrollHeight")
            
            while True:
                # 滚动到底部
                self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
                
                # 等待页面加载
                time.sleep(pause_time)
                
                # 计算新的滚动高度
                new_height = self.driver.execute_script("return document.body.scrollHeight")
                
                if new_height == last_height:
                    break
                    
                last_height = new_height
                
        except Exception as e:
            logger.error(f"滚动页面失败: {e}")
    
    def get_page_source(self) -> str:
        """
        获取页面源码
        
        Returns:
            页面HTML源码
        """
        try:
            return self.driver.page_source
        except Exception as e:
            logger.error(f"获取页面源码失败: {e}")
            return ""
    
    def take_screenshot(self, filename: str) -> bool:
        """
        截图
        
        Args:
            filename: 截图文件名
            
        Returns:
            是否截图成功
        """
        try:
            self.driver.save_screenshot(filename)
            logger.info(f"截图保存: {filename}")
            return True
        except Exception as e:
            logger.error(f"截图失败: {e}")
            return False
    
    def close(self) -> None:
        """关闭浏览器"""
        try:
            if self.driver:
                self.driver.quit()
                logger.info("浏览器已关闭")
        except Exception as e:
            logger.error(f"关闭浏览器失败: {e}")
    
    def __enter__(self):
        """上下文管理器入口"""
        if self.setup_driver():
            return self
        else:
            raise RuntimeError("WebDriver设置失败")
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.close()
