"""
基于Selenium的链家房产数据爬虫
支持手动登录和自动翻页
"""

import pandas as pd
import logging
import re
import time
import random
import os
from typing import List, Dict, Optional
from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from .selenium_base import SeleniumBase
from ..utils.config_loader import config
from ..utils.file_io import save_dataframe, save_dataframe_hybrid, get_data_path

logger = logging.getLogger(__name__)

class SeleniumHousingScraper(SeleniumBase):
    """基于Selenium的链家房产数据爬虫"""
    
    def __init__(self, headless: bool = False):
        """
        初始化爬虫
        
        Args:
            headless: 是否无头模式运行（建议False以便手动登录）
        """
        delay = config.getfloat("SCRAPER_PARAMS", "delay_between_requests", 1.0)  # 减少延时提高速度
        timeout = config.getint("SCRAPER_PARAMS", "timeout", 10)
        super().__init__(headless=headless, delay=delay, timeout=timeout)
        
        # 目标URL - 陆家嘴二手房
        self.base_url = "https://sh.lianjia.com/ershoufang/lujiazui/"
        self.max_pages = config.getint("SCRAPER_PARAMS", "max_pages", 10)
        
        # 数据存储
        self.houses_data = []
        
    def wait_for_manual_login(self) -> bool:
        """
        等待用户手动登录

        Returns:
            是否登录成功
        """
        logger.info("=" * 60)
        logger.info("检测到需要登录，请按以下步骤操作：")
        logger.info("1. 在打开的浏览器窗口中完成登录")
        logger.info("2. 登录成功后，确保能看到房源列表页面")
        logger.info("3. 然后回到控制台输入 'done' 继续爬取")
        logger.info("4. 如果不需要登录或想跳过，输入 'skip'")
        logger.info("=" * 60)

        while True:
            user_input = input("请输入 'done'（登录完成）或 'skip'（跳过登录）: ").strip().lower()

            if user_input == 'done':
                logger.info("用户确认登录完成，正在验证登录状态...")

                # 刷新页面并重新检查登录状态
                time.sleep(2)  # 给页面一点时间
                self.driver.refresh()
                time.sleep(3)  # 等待页面刷新

                # 重新检查是否还需要登录
                if not self.check_login_required():
                    logger.info("✅ 登录验证成功，继续爬取...")
                    return True
                else:
                    logger.warning("⚠️ 登录验证失败，请确保已完成登录")
                    print("请确保：")
                    print("1. 已在浏览器中成功登录")
                    print("2. 当前页面显示房源列表")
                    print("3. 没有登录弹窗或登录按钮")
                    continue

            elif user_input == 'skip':
                logger.info("跳过登录，继续爬取...")
                return True
            else:
                print("请输入 'done' 或 'skip'")
    
    def check_login_required(self) -> bool:
        """
        检查是否需要登录

        Returns:
            是否需要登录
        """
        try:
            # 等待页面加载完成
            time.sleep(2)

            # 首先检查是否有房源列表（表示已登录或不需要登录）
            house_list_selectors = [
                "ul.sellListContent",
                "li.clear.LOGCLICKDATA",
                ".sellListContent li",
                "li[data-lj_action_housedel_id]"
            ]

            for selector in house_list_selectors:
                elements = self.find_elements_safe(By.CSS_SELECTOR, selector)
                if elements and len(elements) > 0:
                    logger.info(f"检测到房源列表（{len(elements)}个房源），无需登录或已登录")
                    return False

            # 检查是否有登录相关的元素
            login_indicators = [
                "//a[contains(text(), '登录')]",
                "//a[contains(text(), '注册')]",
                "//button[contains(text(), '登录')]",
                "//div[contains(@class, 'login')]",
                "//div[contains(@class, 'Login')]",
                "//span[contains(text(), '登录')]",
                "//div[contains(text(), '请登录')]",
                "//div[contains(@class, 'auth')]"
            ]

            login_found = False
            for xpath in login_indicators:
                elements = self.find_elements_safe(By.XPATH, xpath)
                if elements:
                    logger.debug(f"发现登录相关元素: {xpath}")
                    login_found = True
                    break

            # 检查页面标题或URL是否表明需要登录
            page_title = self.driver.title.lower()
            current_url = self.driver.current_url.lower()

            if any(keyword in page_title for keyword in ['登录', 'login', '验证']):
                logger.info(f"页面标题表明需要登录: {page_title}")
                return True

            if any(keyword in current_url for keyword in ['login', 'auth', 'verify']):
                logger.info(f"URL表明需要登录: {current_url}")
                return True

            # 检查是否有验证码或其他阻拦元素
            blocking_elements = [
                "//div[contains(@class, 'captcha')]",
                "//div[contains(@class, 'verify')]",
                "//div[contains(text(), '验证')]",
                "//div[contains(text(), '人机验证')]"
            ]

            for xpath in blocking_elements:
                elements = self.find_elements_safe(By.XPATH, xpath)
                if elements:
                    logger.info("检测到验证码或验证页面")
                    return True

            if login_found:
                logger.info("检测到登录相关元素，可能需要登录")
                return True
            else:
                logger.info("未检测到明确的登录需求，尝试继续")
                return False

        except Exception as e:
            logger.error(f"检查登录状态失败: {e}")
            # 出错时保守处理，假设需要登录
            return True
    
    def parse_house_item(self, house_element) -> Optional[Dict]:
        """
        解析单个房源信息
        
        Args:
            house_element: 房源元素
            
        Returns:
            房源数据字典或None
        """
        try:
            house_data = {}
            
            # 提取房源ID
            house_id = house_element.get_attribute("data-lj_action_housedel_id")
            if not house_id:
                # 尝试从链接中提取
                link_element = house_element.find_element(By.CSS_SELECTOR, "a[href*='ershoufang']")
                if link_element:
                    href = link_element.get_attribute("href")
                    match = re.search(r'/ershoufang/(\d+)\.html', href)
                    if match:
                        house_id = match.group(1)
            
            house_data['house_id'] = house_id
            
            # 提取标题
            title_element = house_element.find_element(By.CSS_SELECTOR, ".title a")
            house_data['title'] = title_element.text.strip() if title_element else ""
            house_data['detail_url'] = title_element.get_attribute("href") if title_element else ""
            
            # 提取位置信息
            position_element = house_element.find_element(By.CSS_SELECTOR, ".positionInfo")
            if position_element:
                position_text = position_element.text.strip()
                # 解析小区和区域
                parts = position_text.split('-')
                if len(parts) >= 2:
                    house_data['community'] = parts[0].strip()
                    house_data['district'] = parts[1].strip()
                else:
                    house_data['community'] = position_text
                    house_data['district'] = ""
            
            # 提取房屋信息
            house_info_element = house_element.find_element(By.CSS_SELECTOR, ".houseInfo")
            if house_info_element:
                house_info_text = house_info_element.text.strip()
                house_data['house_info'] = house_info_text
                
                # 解析具体信息
                info_parts = house_info_text.split('|')
                if len(info_parts) >= 6:
                    house_data['layout'] = info_parts[0].strip()  # 1室1厅
                    house_data['area'] = info_parts[1].strip()    # 47.08平米
                    house_data['orientation'] = info_parts[2].strip()  # 东 北
                    house_data['decoration'] = info_parts[3].strip()   # 精装
                    house_data['floor'] = info_parts[4].strip()        # 25层
                    house_data['build_year'] = info_parts[5].strip()   # 1997年
                    if len(info_parts) > 6:
                        house_data['building_type'] = info_parts[6].strip()  # 塔楼
            
            # 提取价格信息
            total_price_element = house_element.find_element(By.CSS_SELECTOR, ".totalPrice span")
            house_data['total_price'] = total_price_element.text.strip() if total_price_element else ""
            
            unit_price_element = house_element.find_element(By.CSS_SELECTOR, ".unitPrice span")
            if unit_price_element:
                unit_price_text = unit_price_element.text.strip()
                house_data['unit_price'] = unit_price_text
                # 提取数字
                match = re.search(r'([\d,]+)', unit_price_text)
                if match:
                    house_data['unit_price_num'] = match.group(1).replace(',', '')
            
            # 提取关注信息
            follow_element = house_element.find_element(By.CSS_SELECTOR, ".followInfo")
            house_data['follow_info'] = follow_element.text.strip() if follow_element else ""
            
            # 提取标签信息
            tag_elements = house_element.find_elements(By.CSS_SELECTOR, ".tag span")
            tags = [tag.text.strip() for tag in tag_elements if tag.text.strip()]
            house_data['tags'] = '|'.join(tags)
            
            # 添加爬取时间
            house_data['crawl_time'] = pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')
            house_data['source'] = 'selenium_scraper'
            
            return house_data
            
        except NoSuchElementException as e:
            logger.debug(f"解析房源信息时缺少元素: {e}")
            return None
        except Exception as e:
            logger.error(f"解析房源信息失败: {e}")
            return None
    
    def scrape_page(self, page_num: int = 1) -> List[Dict]:
        """
        爬取指定页面的房源数据
        
        Args:
            page_num: 页码
            
        Returns:
            房源数据列表
        """
        try:
            # 构建页面URL
            if page_num == 1:
                url = self.base_url
            else:
                url = f"{self.base_url}pg{page_num}/"
            
            logger.info(f"爬取第 {page_num} 页: {url}")
            
            # 访问页面
            if not self.get_page(url):
                logger.error(f"访问第 {page_num} 页失败")
                return []
            
            # 等待房源列表加载
            house_list_element = self.wait_for_element(By.CSS_SELECTOR, "ul.sellListContent", timeout=15)
            if not house_list_element:
                logger.warning(f"第 {page_num} 页未找到房源列表")
                return []
            
            # 查找所有房源项
            house_items = self.find_elements_safe(By.CSS_SELECTOR, "li.clear.LOGCLICKDATA")
            
            if not house_items:
                logger.warning(f"第 {page_num} 页未找到房源项")
                return []
            
            logger.info(f"第 {page_num} 页找到 {len(house_items)} 个房源")
            
            # 解析每个房源
            page_houses = []
            for i, house_item in enumerate(house_items, 1):
                logger.debug(f"解析第 {page_num} 页第 {i} 个房源")
                
                house_data = self.parse_house_item(house_item)
                if house_data:
                    page_houses.append(house_data)
                
                # 添加小延时
                if i % 5 == 0:
                    time.sleep(random.uniform(0.5, 1.0))
            
            logger.info(f"第 {page_num} 页成功解析 {len(page_houses)} 个房源")
            return page_houses
            
        except Exception as e:
            logger.error(f"爬取第 {page_num} 页失败: {e}")
            return []
    
    def run_scraping(self) -> str:
        """
        运行完整的爬取流程
        
        Returns:
            保存的文件路径
        """
        try:
            logger.info("开始链家房产数据爬取")
            logger.info(f"目标URL: {self.base_url}")
            logger.info(f"最大页数: {self.max_pages}")
            
            # 访问首页
            if not self.get_page(self.base_url):
                raise Exception("无法访问链家首页")
            
            # 检查是否需要登录
            if self.check_login_required():
                if not self.wait_for_manual_login():
                    raise Exception("登录失败或用户取消")
            
            # 开始爬取数据
            all_houses = []
            
            for page in range(1, self.max_pages + 1):
                page_houses = self.scrape_page(page)
                
                if not page_houses:
                    logger.info(f"第 {page} 页无数据，停止爬取")
                    break
                
                all_houses.extend(page_houses)
                
                # 页面间延时 - 减少延时提高速度
                time.sleep(random.uniform(0.5, 1.0))
            
            # 保存数据
            if all_houses:
                filepath = self.save_housing_data(all_houses)
                logger.info(f"爬取完成，共获取 {len(all_houses)} 条房产数据")
                logger.info(f"数据已保存到: {filepath}")
                return filepath
            else:
                logger.warning("未获取到任何房产数据")
                return ""
                
        except Exception as e:
            logger.error(f"爬取过程失败: {e}")
            return ""
    
    def save_housing_data(self, houses_data: List[Dict]) -> str:
        """
        保存房产数据到文件和数据库

        Args:
            houses_data: 房产数据列表

        Returns:
            保存的文件路径
        """
        try:
            if not houses_data:
                logger.warning("没有数据需要保存")
                return ""

            # 转换为DataFrame
            df = pd.DataFrame(houses_data)

            # 数据预处理：添加housing_id字段
            if 'house_id' in df.columns and 'housing_id' not in df.columns:
                df['housing_id'] = df['house_id']
            elif 'housing_id' not in df.columns:
                # 如果没有housing_id，生成一个
                df['housing_id'] = df.index.astype(str) + '_' + pd.Timestamp.now().strftime('%Y%m%d_%H%M%S')

            # 生成文件名
            timestamp = pd.Timestamp.now().strftime('%Y%m%d_%H%M%S')
            filename = f"lianjia_housing_selenium_{timestamp}.csv"
            filepath = get_data_path(f"data/raw/{filename}")

            # 混合保存：同时保存到文件和数据库
            success = save_dataframe_hybrid(
                df=df,
                filepath=filepath,
                table_name='housing_data',
                if_exists='append'
            )

            if success:
                logger.info(f"房产数据已保存到文件和数据库")
                logger.info(f"文件路径: {filepath}")
                logger.info(f"数据行数: {len(df)}")
                logger.info(f"数据列数: {len(df.columns)}")
            else:
                logger.warning("数据保存过程中出现部分错误")

            # 无论数据库保存是否成功，只要文件保存成功就返回文件路径
            if os.path.exists(filepath):
                return filepath
            else:
                logger.error("文件保存失败")
                return ""

        except Exception as e:
            logger.error(f"保存房产数据失败: {e}")
            return ""
