"""
小区房价数据预处理模块

此模块负责：
1. 从原始房屋数据中提取小区信息
2. 计算每个小区的平均房价、每平方米平均房价等统计信息
3. 清洗和标准化小区名称
4. 为后续地理编码准备数据
"""

import pandas as pd
import numpy as np
import logging
from pathlib import Path
import re
from typing import Dict, List, Optional

from src.utils.config_loader import ConfigLoader
from src.utils.logger_setup import setup_logger

# 设置日志
try:
    logger = setup_logger()
except Exception as e:
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    logger = logging.getLogger(__name__)
    logger.warning(f"Could not initialize logger via setup_logger due to: {e}. Using basicConfig.")

class CommunityPricePreprocessor:
    """小区房价数据预处理器"""
    
    def __init__(self):
        self.config = ConfigLoader()
        
    def clean_community_name(self, name: str) -> Optional[str]:
        """清洗小区名称"""
        if not name or pd.isna(name):
            return None
            
        name = str(name).strip()
        
        # 过滤无效名称
        invalid_names = ['nan', 'None', '', '未知', '暂无', '其他', 'null', 'NaN']
        if name.lower() in [n.lower() for n in invalid_names]:
            return None
            
        # 去除括号内容和多余描述
        name = re.sub(r'[（(].*?[）)]', '', name)
        name = re.sub(r'小区$', '', name)
        name = re.sub(r'公寓$', '', name)
        name = re.sub(r'花园$', '', name)
        name = re.sub(r'大厦$', '', name)
        name = re.sub(r'广场$', '', name)
        name = re.sub(r'\s+', '', name)
        
        # 检查长度
        if len(name) < 2 or len(name) > 30:
            return None
            
        return name
    
    def extract_numeric_price(self, price_str: str) -> Optional[float]:
        """从价格字符串中提取数值"""
        if not price_str or pd.isna(price_str):
            return None
            
        price_str = str(price_str).strip()
        
        # 移除逗号和其他分隔符
        price_str = re.sub(r'[,，]', '', price_str)
        
        # 提取数字
        numbers = re.findall(r'\d+\.?\d*', price_str)
        if numbers:
            try:
                return float(numbers[0])
            except ValueError:
                return None
        return None
    
    def extract_area_from_house_info(self, house_info: str) -> Optional[float]:
        """从房屋信息中提取面积"""
        if not house_info or pd.isna(house_info):
            return None
            
        # 查找面积模式：数字+平米/平方米/㎡
        area_patterns = [
            r'(\d+\.?\d*)\s*平米',
            r'(\d+\.?\d*)\s*平方米',
            r'(\d+\.?\d*)\s*㎡',
            r'(\d+\.?\d*)\s*平'
        ]
        
        for pattern in area_patterns:
            match = re.search(pattern, str(house_info))
            if match:
                try:
                    return float(match.group(1))
                except ValueError:
                    continue
        return None
    
    def preprocess_housing_data(self, input_file: str, output_file: str) -> str:
        """
        预处理房屋数据，生成小区统计信息
        
        Args:
            input_file: 原始房屋数据文件路径
            output_file: 输出文件路径
            
        Returns:
            处理后的文件路径
        """
        logger.info("开始预处理房屋数据...")
        
        # 加载原始数据
        try:
            df = pd.read_csv(input_file)
            logger.info(f"成功加载房屋数据: {len(df)} 条记录")
            logger.info(f"数据列: {list(df.columns)}")
        except Exception as e:
            logger.error(f"加载数据失败: {e}")
            return ""
        
        # 数据清洗和处理
        processed_data = []
        
        for idx, row in df.iterrows():
            try:
                # 清洗小区名称
                community_raw = row.get('community', '')
                community_clean = self.clean_community_name(community_raw)
                
                if not community_clean:
                    continue
                
                # 提取价格信息
                total_price = self.extract_numeric_price(row.get('total_price', ''))
                unit_price_num = row.get('unit_price_num', None)
                
                # 如果unit_price_num存在且是数值型，使用它
                if pd.notna(unit_price_num):
                    try:
                        unit_price = float(unit_price_num)
                    except (ValueError, TypeError):
                        unit_price = self.extract_numeric_price(row.get('unit_price', ''))
                else:
                    unit_price = self.extract_numeric_price(row.get('unit_price', ''))
                
                # 提取面积信息
                area = None
                if 'area' in row and pd.notna(row['area']):
                    area = self.extract_numeric_price(row['area'])
                
                if not area and 'house_info' in row:
                    area = self.extract_area_from_house_info(row['house_info'])
                
                # 构建记录
                record = {
                    'community_raw': community_raw,
                    'community_clean': community_clean,
                    'district': row.get('district', ''),
                    'total_price': total_price,
                    'unit_price': unit_price,
                    'area': area,
                    'layout': row.get('layout', ''),
                    'build_year': row.get('build_year', ''),
                    'floor': row.get('floor', ''),
                    'decoration': row.get('decoration', ''),
                    'house_id': row.get('house_id', ''),
                    'crawl_time': row.get('crawl_time', '')
                }
                
                processed_data.append(record)
                
            except Exception as e:
                logger.warning(f"处理第{idx}行数据失败: {e}")
                continue
        
        # 转换为DataFrame
        processed_df = pd.DataFrame(processed_data)
        logger.info(f"数据处理完成，有效记录: {len(processed_df)} 条")
        
        # 按小区聚合统计
        logger.info("开始计算小区统计信息...")
        community_stats = self.calculate_community_statistics(processed_df)
        
        # 保存结果
        try:
            # 确保输出目录存在
            output_path = Path(output_file)
            output_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 保存处理后的详细数据
            detail_file = output_path.parent / f"{output_path.stem}_detail.csv"
            processed_df.to_csv(detail_file, index=False, encoding='utf-8-sig')
            logger.info(f"详细数据已保存到: {detail_file}")
            
            # 保存小区统计数据
            community_stats.to_csv(output_file, index=False, encoding='utf-8-sig')
            logger.info(f"小区统计数据已保存到: {output_file}")
            
            # 打印统计摘要
            self.print_summary(community_stats)
            
            return str(output_file)
            
        except Exception as e:
            logger.error(f"保存数据失败: {e}")
            return ""
    
    def calculate_community_statistics(self, df: pd.DataFrame) -> pd.DataFrame:
        """计算小区统计信息"""
        
        community_groups = df.groupby('community_clean')
        stats_list = []
        
        for community_name, group in community_groups:
            try:
                # 基础信息
                stats = {
                    'community_name': community_name,
                    'district': group['district'].mode().iloc[0] if not group['district'].empty else '',
                    'house_count': len(group)
                }
                
                # 总价统计
                total_prices = group['total_price'].dropna()
                if len(total_prices) > 0:
                    stats.update({
                        'avg_total_price': total_prices.mean(),
                        'median_total_price': total_prices.median(),
                        'min_total_price': total_prices.min(),
                        'max_total_price': total_prices.max(),
                        'std_total_price': total_prices.std(),
                        'total_price_count': len(total_prices)
                    })
                else:
                    stats.update({
                        'avg_total_price': None,
                        'median_total_price': None,
                        'min_total_price': None,
                        'max_total_price': None,
                        'std_total_price': None,
                        'total_price_count': 0
                    })
                
                # 单价统计
                unit_prices = group['unit_price'].dropna()
                if len(unit_prices) > 0:
                    stats.update({
                        'avg_unit_price': unit_prices.mean(),
                        'median_unit_price': unit_prices.median(),
                        'min_unit_price': unit_prices.min(),
                        'max_unit_price': unit_prices.max(),
                        'std_unit_price': unit_prices.std(),
                        'unit_price_count': len(unit_prices)
                    })
                else:
                    stats.update({
                        'avg_unit_price': None,
                        'median_unit_price': None,
                        'min_unit_price': None,
                        'max_unit_price': None,
                        'std_unit_price': None,
                        'unit_price_count': 0
                    })
                
                # 面积统计
                areas = group['area'].dropna()
                if len(areas) > 0:
                    stats.update({
                        'avg_area': areas.mean(),
                        'median_area': areas.median(),
                        'min_area': areas.min(),
                        'max_area': areas.max(),
                        'std_area': areas.std(),
                        'area_count': len(areas)
                    })
                else:
                    stats.update({
                        'avg_area': None,
                        'median_area': None,
                        'min_area': None,
                        'max_area': None,
                        'std_area': None,
                        'area_count': 0
                    })
                
                # 建筑年份统计
                build_years = group['build_year'].dropna()
                if len(build_years) > 0:
                    # 提取年份数字
                    year_numbers = []
                    for year_str in build_years:
                        year_match = re.search(r'(\d{4})', str(year_str))
                        if year_match:
                            try:
                                year_numbers.append(int(year_match.group(1)))
                            except ValueError:
                                continue
                    
                    if year_numbers:
                        stats.update({
                            'avg_build_year': np.mean(year_numbers),
                            'min_build_year': min(year_numbers),
                            'max_build_year': max(year_numbers)
                        })
                    else:
                        stats.update({
                            'avg_build_year': None,
                            'min_build_year': None,
                            'max_build_year': None
                        })
                else:
                    stats.update({
                        'avg_build_year': None,
                        'min_build_year': None,
                        'max_build_year': None
                    })
                
                # 装修情况统计
                decoration_counts = group['decoration'].value_counts()
                stats['main_decoration'] = decoration_counts.index[0] if len(decoration_counts) > 0 else ''
                
                # 户型统计
                layout_counts = group['layout'].value_counts()
                stats['main_layout'] = layout_counts.index[0] if len(layout_counts) > 0 else ''
                
                stats_list.append(stats)
                
            except Exception as e:
                logger.warning(f"计算小区 {community_name} 统计信息失败: {e}")
                continue
        
        return pd.DataFrame(stats_list)
    
    def print_summary(self, community_stats: pd.DataFrame):
        """打印统计摘要"""
        logger.info("="*60)
        logger.info("小区房价数据统计摘要")
        logger.info("="*60)
        
        logger.info(f"总小区数量: {len(community_stats)}")
        
        # 单价统计
        valid_unit_prices = community_stats['avg_unit_price'].dropna()
        if len(valid_unit_prices) > 0:
            logger.info(f"有单价数据的小区: {len(valid_unit_prices)}")
            logger.info(f"单价范围: {valid_unit_prices.min():.0f} - {valid_unit_prices.max():.0f} 元/平米")
            logger.info(f"平均单价: {valid_unit_prices.mean():.0f} 元/平米")
            logger.info(f"单价中位数: {valid_unit_prices.median():.0f} 元/平米")
        
        # 总价统计
        valid_total_prices = community_stats['avg_total_price'].dropna()
        if len(valid_total_prices) > 0:
            logger.info(f"有总价数据的小区: {len(valid_total_prices)}")
            logger.info(f"总价范围: {valid_total_prices.min():.0f} - {valid_total_prices.max():.0f} 万元")
            logger.info(f"平均总价: {valid_total_prices.mean():.0f} 万元")
        
        # 面积统计
        valid_areas = community_stats['avg_area'].dropna()
        if len(valid_areas) > 0:
            logger.info(f"有面积数据的小区: {len(valid_areas)}")
            logger.info(f"面积范围: {valid_areas.min():.1f} - {valid_areas.max():.1f} 平米")
            logger.info(f"平均面积: {valid_areas.mean():.1f} 平米")
        
        # 显示前10个小区的示例数据
        logger.info("\n前10个小区的统计示例:")
        logger.info("-"*60)
        for idx, row in community_stats.head(10).iterrows():
            logger.info(f"小区: {row['community_name']}")
            logger.info(f"  区域: {row['district']}")
            logger.info(f"  房源数: {row['house_count']}")
            if pd.notna(row['avg_unit_price']):
                logger.info(f"  平均单价: {row['avg_unit_price']:.0f} 元/平米")
            if pd.notna(row['avg_total_price']):
                logger.info(f"  平均总价: {row['avg_total_price']:.0f} 万元")
            if pd.notna(row['avg_area']):
                logger.info(f"  平均面积: {row['avg_area']:.1f} 平米")
            logger.info("")

def main():
    """主函数"""
    logger.info("开始小区房价数据预处理...")
    
    # 文件路径
    project_root = Path(__file__).parent.parent.parent
    input_file = project_root / "data" / "raw" / "lianjia_housing_selenium_20250604_211231.csv"
    output_file = project_root / "data" / "processed" / "community_price_stats.csv"
    
    # 创建预处理器
    preprocessor = CommunityPricePreprocessor()
    
    # 执行预处理
    result_file = preprocessor.preprocess_housing_data(str(input_file), str(output_file))
    
    if result_file:
        logger.info(f"预处理完成！结果保存到: {result_file}")
    else:
        logger.error("预处理失败！")

if __name__ == "__main__":
    main()
