"""
基于小区的POI数据收集器
为每个小区收集周边POI信息并计算评分
"""

import pandas as pd
import numpy as np
import logging
from typing import Dict, List
from tqdm import tqdm
from ..utils.config_loader import config
from ..utils.api_helpers import search_poi_around_gaode
from ..utils.file_io import load_dataframe, save_dataframe, save_json, get_data_path

logger = logging.getLogger(__name__)

class CommunityPOICollector:
    """基于小区的POI收集器"""
    
    def __init__(self):
        self.search_radius = config.getint("SCRAPER_PARAMS", "poi_search_radius_m", 1000)
        self.poi_types = self._load_poi_types()
        self.poi_weights = self._load_poi_weights()
        
    def _load_poi_types(self) -> Dict[str, str]:
        """加载POI类型配置 - 使用固定的高德POI分类编码"""
        # 高德POI分类编码 - 固定配置，不需要在config文件中修改
        poi_types = {
            'catering': '050000',           # 餐饮服务
            'shopping': '060000',           # 购物服务
            'medical': '090000',            # 医疗保健
            'education': '140000',          # 科教文化
            'leisure': '110000|070000|080000',  # 休闲娱乐|体育休闲|生活服务
            'transportation': '150500|150700',  # 地铁站|公交站
            'finance': '160000',            # 金融保险
            'government': '170000'          # 政府机构
        }

        logger.info(f"加载POI类型配置: {list(poi_types.keys())}")
        return poi_types
    
    def _load_poi_weights(self) -> Dict[str, float]:
        """加载POI权重配置"""
        # 可以从配置文件读取，这里使用默认权重
        weights = {
            'transportation': 0.25,  # 交通便利性权重最高
            'catering': 0.20,        # 餐饮
            'shopping': 0.15,        # 购物
            'medical': 0.15,         # 医疗
            'education': 0.10,       # 教育
            'leisure': 0.10,         # 休闲娱乐
            'finance': 0.05          # 金融服务
        }
        
        logger.info(f"POI权重配置: {weights}")
        return weights
    
    def collect_community_poi_data(self, housing_file: str = None) -> str:
        """
        收集小区POI数据 - 先从房产数据中清洗得到所有小区名，然后对每个小区调用高德API

        Args:
            housing_file: 房产数据文件路径

        Returns:
            保存的POI数据文件路径
        """
        logger.info("=" * 50)
        logger.info("开始小区POI数据收集")
        logger.info("=" * 50)

        # 1. 加载房产数据
        if not housing_file:
            housing_file = config.get("PATHS", "processed_housing_data",
                                    "data/processed/housing_cleaned_geocoded.csv")

        housing_df = load_dataframe(get_data_path(housing_file))
        if housing_df is None:
            logger.error(f"无法加载房产数据: {housing_file}")
            return ""

        logger.info(f"加载房产数据: {len(housing_df)} 条记录")

        # 2. 提取和清洗小区名称
        communities = self._extract_and_clean_communities(housing_df)
        if not communities:
            logger.error("未找到有效的小区名称")
            return ""

        logger.info(f"提取到 {len(communities)} 个唯一小区")

        # 3. 对小区进行地理编码
        community_coords = self._geocode_communities(communities)
        if not community_coords:
            logger.error("小区地理编码失败")
            return ""

        logger.info(f"成功地理编码 {len(community_coords)} 个小区")

        # 4. 收集每个小区的POI数据
        community_poi_data = self._collect_poi_for_communities(community_coords)

        # 5. 保存原始POI数据
        raw_output_file = config.get("PATHS", "raw_poi_data",
                                   "data/raw/community_poi_raw.json")
        raw_output_path = get_data_path(raw_output_file)
        save_json(community_poi_data, raw_output_path)

        # 6. 处理和聚合POI数据
        processed_df = self._process_poi_data(community_poi_data)

        # 7. 保存处理后的数据
        processed_output_file = config.get("PATHS", "community_poi_data",
                                         "data/processed/community_poi_aggregated.csv")
        processed_output_path = get_data_path(processed_output_file)
        save_dataframe(processed_df, processed_output_path)

        logger.info(f"小区POI数据收集完成: {len(community_poi_data)} 个小区")
        logger.info(f"原始数据: {raw_output_path}")
        logger.info(f"处理后数据: {processed_output_path}")

        logger.info("=" * 50)
        logger.info("小区POI数据收集完成")
        logger.info("=" * 50)

        return processed_output_path

    def _extract_and_clean_communities(self, housing_df: pd.DataFrame) -> set:
        """提取和清洗小区名称"""
        from ..utils.api_helpers import clean_address_for_geocoding

        logger.info("开始提取和清洗小区名称...")

        communities = set()

        # 从不同字段提取小区名称
        for col in ['community', 'title', 'house_info']:
            if col in housing_df.columns:
                # 提取小区名称
                if col == 'title':
                    # 从标题中提取小区名（通常在标题开头）
                    titles = housing_df[col].astype(str)
                    for title in titles:
                        community = self._extract_community_from_title(title)
                        if community:
                            communities.add(community)
                elif col == 'house_info':
                    # 从房源信息中提取小区名
                    house_infos = housing_df[col].astype(str)
                    for info in house_infos:
                        community = self._extract_community_from_house_info(info)
                        if community:
                            communities.add(community)
                else:
                    # 直接从community字段获取
                    community_names = housing_df[col].dropna().astype(str)
                    for name in community_names:
                        cleaned = self._clean_community_name(name)
                        if cleaned:
                            communities.add(cleaned)

        # 进一步清洗小区名称
        cleaned_communities = set()
        for community in communities:
            cleaned = self._clean_community_name(community)
            if cleaned and len(cleaned) >= 2:
                cleaned_communities.add(cleaned)

        logger.info(f"清洗前小区数: {len(communities)}, 清洗后: {len(cleaned_communities)}")

        return cleaned_communities

    def _extract_community_from_title(self, title: str) -> str:
        """从标题中提取小区名"""
        from ..utils.api_helpers import clean_address_for_geocoding

        if not title or title.strip() == '':
            return None

        # 清洗标题
        title = clean_address_for_geocoding(title)
        if not title:
            return None

        # 尝试提取小区名（通常在标题的前半部分）
        parts = title.split()
        if len(parts) > 0:
            # 取第一个有意义的部分作为小区名
            community = parts[0]
            return self._clean_community_name(community)

        return None

    def _extract_community_from_house_info(self, house_info: str) -> str:
        """从房源信息中提取小区名"""
        if not house_info or house_info.strip() == '':
            return None

        # 格式通常是：浦东-区域-小区名称/面积/朝向/房型/楼层
        import re
        match = re.search(r'浦东-[^-]+-([^/]+)', house_info)
        if match:
            community = match.group(1).strip()
            return self._clean_community_name(community)

        return None

    def _clean_community_name(self, name: str) -> str:
        """清洗小区名称"""
        if not name or name.strip() == '':
            return None

        name = str(name).strip()

        # 去除无效名称
        invalid_names = ['nan', 'None', '', '未知', '暂无', '其他', 'null']
        if name.lower() in [n.lower() for n in invalid_names]:
            return None

        # 去除常见的描述词
        import re
        name = re.sub(r'[（(].*?[）)]', '', name)  # 去除括号内容
        name = re.sub(r'小区$', '', name)         # 去除末尾的"小区"
        name = re.sub(r'公寓$', '', name)         # 去除末尾的"公寓"
        name = re.sub(r'花园$', '', name)         # 去除末尾的"花园"
        name = re.sub(r'大厦$', '', name)         # 去除末尾的"大厦"
        name = re.sub(r'\s+', '', name)          # 去除所有空格

        # 检查长度
        if len(name) < 2 or len(name) > 20:
            return None

        return name

    def _geocode_communities(self, communities: set) -> dict:
        """对小区进行地理编码"""
        from ..utils.api_helpers import geocode_address_gaode
        from tqdm import tqdm
        import time

        logger.info(f"开始对 {len(communities)} 个小区进行地理编码...")

        community_coords = {}
        failed_count = 0

        for i, community in enumerate(tqdm(communities, desc="地理编码小区"), 1):
            try:
                # 构建完整地址
                address = f"上海市浦东新区{community}"

                # 地理编码
                coords = geocode_address_gaode(address, city="上海")

                if coords:
                    community_coords[community] = coords
                    logger.debug(f"小区地理编码成功: {community} -> {coords}")
                else:
                    failed_count += 1
                    logger.warning(f"小区地理编码失败: {community}")

                # 控制API调用频率
                time.sleep(0.8)

            except Exception as e:
                failed_count += 1
                logger.error(f"小区地理编码异常: {community}, 错误: {e}")
                continue

        success_rate = len(community_coords) / len(communities) * 100
        logger.info(f"小区地理编码完成，成功率: {len(community_coords)}/{len(communities)} ({success_rate:.1f}%)")

        return community_coords

    def _collect_poi_for_communities(self, community_coords: dict) -> list:
        """为每个小区收集POI数据"""
        from tqdm import tqdm
        import time

        logger.info(f"开始为 {len(community_coords)} 个小区收集POI数据...")

        community_poi_data = []

        for community, coords in tqdm(community_coords.items(), desc="收集POI数据"):
            try:
                # 构建坐标字符串
                coords_str = f"{coords[0]},{coords[1]}"

                # 收集各类POI
                community_poi = {
                    'community_name': community,
                    'longitude': coords[0],
                    'latitude': coords[1],
                    'poi_data': {}
                }

                # 按类别收集POI
                total_poi_count = 0
                for category, type_codes in self.poi_types.items():
                    try:
                        pois = search_poi_around_gaode(
                            coords_str,
                            type_codes,
                            radius=self.search_radius,
                            page_size=20,  # 减少每页数量以避免超限
                            max_pages=3    # 限制页数
                        )

                        community_poi['poi_data'][category] = pois
                        poi_count = len(pois) if pois else 0
                        total_poi_count += poi_count

                        logger.debug(f"小区 {community} {category} POI: {poi_count} 条")

                        # 控制API调用频率
                        time.sleep(0.5)

                    except Exception as e:
                        logger.error(f"收集 {community} {category} POI失败: {e}")
                        community_poi['poi_data'][category] = []

                community_poi['total_poi_count'] = total_poi_count
                community_poi_data.append(community_poi)

                logger.info(f"小区 {community} POI收集完成，共 {total_poi_count} 条")

            except Exception as e:
                logger.error(f"收集小区 {community} POI数据失败: {e}")
                continue

        logger.info(f"POI数据收集完成，共处理 {len(community_poi_data)} 个小区")
        return community_poi_data

    def _collect_single_community_poi(self, community_row) -> Dict:
        """收集单个小区的POI数据"""
        community_name = community_row['community_name']
        longitude = community_row['longitude']
        latitude = community_row['latitude']
        
        # 构建坐标字符串
        coords_str = f"{longitude},{latitude}"
        
        logger.debug(f"收集小区 {community_name} 的POI数据")
        
        community_poi = {
            'community_name': community_name,
            'longitude': longitude,
            'latitude': latitude,
            'distance_to_cbd_km': community_row.get('distance_to_cbd_km'),
            'poi_data': {}
        }
        
        # 按类别收集POI
        for category, type_codes in self.poi_types.items():
            try:
                logger.debug(f"收集 {community_name} 的 {category} POI")
                
                pois = search_poi_around_gaode(
                    coords_str,
                    type_codes,
                    radius=self.search_radius,
                    page_size=20,  # 减少每页数量以避免超限
                    max_pages=3    # 限制页数
                )
                
                community_poi['poi_data'][category] = pois
                logger.debug(f"{community_name} {category} POI: {len(pois)} 个")
                
                # 添加延时避免API限流
                import time
                time.sleep(0.3)
                
            except Exception as e:
                logger.warning(f"收集 {community_name} {category} POI失败: {e}")
                community_poi['poi_data'][category] = []
        
        return community_poi
    
    def _process_poi_data(self, community_poi_data: List[Dict]) -> pd.DataFrame:
        """处理和聚合POI数据"""
        logger.info("处理POI数据并计算评分...")
        
        processed_data = []
        
        for community_poi in community_poi_data:
            try:
                processed_record = self._process_single_community_poi(community_poi)
                processed_data.append(processed_record)
            except Exception as e:
                logger.error(f"处理小区POI数据失败 ({community_poi.get('community_name')}): {e}")
                continue
        
        df = pd.DataFrame(processed_data)
        
        # 计算综合便利度指数
        df = self._calculate_convenience_index(df)
        
        return df
    
    def _process_single_community_poi(self, community_poi: Dict) -> Dict:
        """处理单个小区的POI数据"""
        result = {
            'community_name': community_poi['community_name'],
            'longitude': community_poi['longitude'],
            'latitude': community_poi['latitude'],
            'distance_to_cbd_km': community_poi.get('distance_to_cbd_km')
        }
        
        poi_data = community_poi.get('poi_data', {})
        
        # 处理每个POI类别
        for category in self.poi_types.keys():
            pois = poi_data.get(category, [])
            
            # 基础统计
            result[f'{category}_count'] = len(pois)
            
            if pois:
                # 计算平均评分
                ratings = self._extract_ratings(pois)
                if ratings:
                    result[f'{category}_avg_rating'] = np.mean(ratings)
                    result[f'{category}_max_rating'] = np.max(ratings)
                else:
                    result[f'{category}_avg_rating'] = 0
                    result[f'{category}_max_rating'] = 0
                
                # 计算距离统计
                distances = self._calculate_distances(pois, community_poi)
                if distances:
                    result[f'{category}_avg_distance'] = np.mean(distances)
                    result[f'{category}_min_distance'] = np.min(distances)
                else:
                    result[f'{category}_avg_distance'] = self.search_radius
                    result[f'{category}_min_distance'] = self.search_radius
                
                # 计算密度得分（每平方公里POI数量）
                area_km2 = np.pi * (self.search_radius / 1000) ** 2
                result[f'{category}_density'] = len(pois) / area_km2
                
            else:
                # 无POI时的默认值
                result[f'{category}_avg_rating'] = 0
                result[f'{category}_max_rating'] = 0
                result[f'{category}_avg_distance'] = self.search_radius
                result[f'{category}_min_distance'] = self.search_radius
                result[f'{category}_density'] = 0
        
        return result
    
    def _extract_ratings(self, pois: List[Dict]) -> List[float]:
        """提取POI评分"""
        ratings = []
        
        for poi in pois:
            rating = None
            
            # 检查不同的评分字段
            biz_ext = poi.get('biz_ext', {})
            if isinstance(biz_ext, dict):
                rating = biz_ext.get('rating') or biz_ext.get('score')
            
            if rating is None:
                rating = poi.get('rating') or poi.get('score')
            
            if rating is not None:
                try:
                    rating_float = float(rating)
                    if 0 <= rating_float <= 5:
                        ratings.append(rating_float)
                except (ValueError, TypeError):
                    continue
        
        return ratings
    
    def _calculate_distances(self, pois: List[Dict], community_poi: Dict) -> List[float]:
        """计算POI到小区的距离"""
        distances = []
        community_lng = community_poi['longitude']
        community_lat = community_poi['latitude']
        
        for poi in pois:
            location = poi.get('location', '')
            if location:
                try:
                    poi_lng, poi_lat = map(float, location.split(','))
                    distance = self._haversine_distance(
                        community_lat, community_lng, poi_lat, poi_lng
                    )
                    distances.append(distance)
                except (ValueError, IndexError):
                    continue
        
        return distances
    
    def _haversine_distance(self, lat1: float, lon1: float, lat2: float, lon2: float) -> float:
        """计算两点间距离（米）"""
        from math import radians, cos, sin, asin, sqrt
        
        lat1, lon1, lat2, lon2 = map(radians, [lat1, lon1, lat2, lon2])
        dlat = lat2 - lat1
        dlon = lon2 - lon1
        a = sin(dlat/2)**2 + cos(lat1) * cos(lat2) * sin(dlon/2)**2
        c = 2 * asin(sqrt(a))
        r = 6371000  # 地球半径（米）
        return c * r
    
    def _calculate_convenience_index(self, df: pd.DataFrame) -> pd.DataFrame:
        """计算综合便利度指数"""
        logger.info("计算综合便利度指数...")
        
        # 为每个类别计算标准化得分
        category_scores = {}
        
        for category, weight in self.poi_weights.items():
            count_col = f'{category}_count'
            density_col = f'{category}_density'
            rating_col = f'{category}_avg_rating'
            
            if count_col in df.columns:
                # 数量得分（标准化到0-100）
                count_score = self._normalize_score(df[count_col])
                
                # 密度得分
                density_score = 0
                if density_col in df.columns:
                    density_score = self._normalize_score(df[density_col])
                
                # 质量得分（基于评分）
                quality_score = 0
                if rating_col in df.columns:
                    quality_score = df[rating_col] * 20  # 5分制转100分制
                
                # 综合该类别得分
                category_score = (count_score * 0.5 + density_score * 0.3 + quality_score * 0.2)
                category_scores[category] = category_score * weight
        
        # 计算总便利度指数
        convenience_index = pd.Series([0.0] * len(df), index=df.index)
        
        for category, score in category_scores.items():
            convenience_index += score
        
        df['convenience_index'] = convenience_index
        
        # 便利度等级
        df['convenience_level'] = pd.cut(
            df['convenience_index'],
            bins=[0, 20, 40, 60, 80, 100],
            labels=['很不便', '不便', '一般', '便利', '很便利']
        )
        
        logger.info(f"便利度指数计算完成，平均得分: {convenience_index.mean():.1f}")
        
        return df
    
    def _normalize_score(self, series: pd.Series) -> pd.Series:
        """标准化得分到0-100范围"""
        if series.empty or series.max() == series.min():
            return pd.Series([50] * len(series), index=series.index)
        
        min_val = series.min()
        max_val = series.max()
        
        normalized = 100 * (series - min_val) / (max_val - min_val)
        return normalized
