#!/usr/bin/env python3
"""
小区房源数据分析主程序
按小区归类房源数据，计算小区均价和POI指数
"""

import logging
import sys
import argparse
import pandas as pd
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.utils.config_loader import config
from src.utils.logger_setup import setup_logger
from src.utils.file_io import load_dataframe, get_data_path
from src.data_processing.clean_housing_data import HousingDataCleaner
from src.data_processing.community_poi_aggregator import CommunityPOIAggregator

def main():
    """主程序入口"""
    parser = argparse.ArgumentParser(description='小区房源数据分析系统')
    parser.add_argument('--step', choices=['all', 'aggregate', 'poi', 'analysis'], 
                       default='all', help='执行步骤')
    parser.add_argument('--housing-file', help='房源数据文件路径')
    parser.add_argument('--poi-file', help='POI数据文件路径')
    parser.add_argument('--top-n', type=int, default=10, help='显示前N个小区')
    
    args = parser.parse_args()
    
    # 设置日志
    setup_logger()
    logger = logging.getLogger(__name__)
    
    logger.info("=" * 60)
    logger.info("小区房源数据分析系统启动")
    logger.info("=" * 60)
    
    try:
        community_file = ""
        community_poi_file = ""
        
        if args.step in ['all', 'aggregate']:
            logger.info("步骤1: 按小区归类房源数据并计算均价")
            
            # 房源数据清洗和小区聚合
            housing_cleaner = HousingDataCleaner()
            
            # 如果没有指定房源文件，先进行数据清洗
            if not args.housing_file:
                logger.info("清洗房源数据...")
                cleaned_housing_file = housing_cleaner.clean_housing_data()
                if not cleaned_housing_file:
                    logger.error("房源数据清洗失败")
                    return 1
            else:
                cleaned_housing_file = args.housing_file
            
            # 按小区聚合房源数据
            logger.info("按小区聚合房源数据...")
            community_file = housing_cleaner.aggregate_by_community(cleaned_housing_file)
            
            if not community_file:
                logger.error("小区数据聚合失败")
                return 1
            
            # 显示小区聚合摘要
            show_community_summary(community_file)
        
        if args.step in ['all', 'poi']:
            logger.info("步骤2: 聚合小区POI数据并计算便利度指数")
            
            # POI数据聚合
            poi_aggregator = CommunityPOIAggregator()
            
            housing_file = args.housing_file if args.housing_file else config.get(
                "PATHS", "processed_housing_data", "data/processed/housing_cleaned_geocoded.csv"
            )
            poi_file = args.poi_file if args.poi_file else config.get(
                "PATHS", "processed_poi_data", "data/processed/poi_gaode_aggregated.csv"
            )
            
            community_poi_file = poi_aggregator.aggregate_community_poi_data(
                housing_file, poi_file
            )
            
            if not community_poi_file:
                logger.error("小区POI数据聚合失败")
                return 1
            
            # 显示POI聚合摘要
            show_poi_summary(community_poi_file)
        
        if args.step in ['all', 'analysis']:
            logger.info("步骤3: 综合分析小区数据")
            
            # 合并小区房源数据和POI数据
            if not community_file:
                community_file = config.get("PATHS", "community_aggregated_data",
                                          "data/processed/community_aggregated_housing.csv")
            
            if not community_poi_file:
                community_poi_file = config.get("PATHS", "community_poi_data",
                                               "data/processed/community_poi_aggregated.csv")
            
            final_file = merge_community_data(community_file, community_poi_file)
            
            if not final_file:
                logger.error("小区数据合并失败")
                return 1
            
            # 显示最终分析结果
            show_final_analysis(final_file, args.top_n)
        
        logger.info("=" * 60)
        logger.info("小区房源数据分析完成")
        logger.info("=" * 60)
        
        return 0
        
    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        return 1

def show_community_summary(community_file: str):
    """显示小区聚合摘要"""
    logger = logging.getLogger(__name__)
    
    try:
        df = load_dataframe(get_data_path(community_file))
        if df is None:
            logger.warning("无法加载小区数据")
            return
        
        logger.info("小区数据聚合摘要:")
        logger.info(f"  总小区数: {len(df)}")
        
        if 'housing_count' in df.columns:
            logger.info(f"  总房源数: {df['housing_count'].sum()}")
            logger.info(f"  平均每小区房源数: {df['housing_count'].mean():.1f}")
        
        if 'avg_unit_price' in df.columns:
            valid_prices = df['avg_unit_price'].dropna()
            if len(valid_prices) > 0:
                logger.info(f"  小区平均单价: {valid_prices.mean():.0f} 元/平米")
                logger.info(f"  单价范围: {valid_prices.min():.0f} - {valid_prices.max():.0f} 元/平米")
        
        if 'distance_to_cbd_km' in df.columns:
            valid_distances = df['distance_to_cbd_km'].dropna()
            if len(valid_distances) > 0:
                logger.info(f"  平均距离CBD: {valid_distances.mean():.1f} 公里")
        
    except Exception as e:
        logger.error(f"显示小区摘要失败: {e}")

def show_poi_summary(community_poi_file: str):
    """显示POI聚合摘要"""
    logger = logging.getLogger(__name__)
    
    try:
        df = load_dataframe(get_data_path(community_poi_file))
        if df is None:
            logger.warning("无法加载小区POI数据")
            return
        
        logger.info("小区POI数据聚合摘要:")
        logger.info(f"  总小区数: {len(df)}")
        
        if 'poi_convenience_index' in df.columns:
            valid_index = df['poi_convenience_index'].dropna()
            if len(valid_index) > 0:
                logger.info(f"  平均POI便利度指数: {valid_index.mean():.1f}")
                logger.info(f"  便利度指数范围: {valid_index.min():.1f} - {valid_index.max():.1f}")
        
        # 显示各类POI统计
        poi_categories = ['catering', 'shopping', 'medical', 'education', 'leisure', 'transportation', 'finance']
        for category in poi_categories:
            count_col = f'{category}_avg_count'
            if count_col in df.columns:
                avg_count = df[count_col].mean()
                logger.info(f"  平均{category}POI数量: {avg_count:.1f}")
        
    except Exception as e:
        logger.error(f"显示POI摘要失败: {e}")

def merge_community_data(community_file: str, community_poi_file: str) -> str:
    """合并小区房源数据和POI数据"""
    logger = logging.getLogger(__name__)
    
    try:
        # 加载数据
        community_df = load_dataframe(get_data_path(community_file))
        poi_df = load_dataframe(get_data_path(community_poi_file))
        
        if community_df is None or poi_df is None:
            logger.error("无法加载小区数据或POI数据")
            return ""
        
        # 合并数据
        merged_df = community_df.merge(
            poi_df,
            on='community_name',
            how='outer',
            suffixes=('', '_poi')
        )
        
        # 处理重复列
        duplicate_cols = ['longitude_poi', 'latitude_poi', 'housing_count_poi']
        for col in duplicate_cols:
            if col in merged_df.columns:
                base_col = col.replace('_poi', '')
                if base_col in merged_df.columns:
                    # 用POI数据填补缺失值
                    merged_df[base_col] = merged_df[base_col].fillna(merged_df[col])
                merged_df.drop(columns=[col], inplace=True)
        
        # 保存合并后的数据
        output_file = config.get("PATHS", "final_community_data",
                                "data/final/final_community_analysis.csv")
        output_path = get_data_path(output_file)
        
        from src.utils.file_io import save_dataframe
        save_dataframe(merged_df, output_path)
        
        logger.info(f"小区数据合并完成，共 {len(merged_df)} 个小区")
        logger.info(f"数据已保存到: {output_path}")
        
        return output_path
        
    except Exception as e:
        logger.error(f"合并小区数据失败: {e}")
        return ""

def show_final_analysis(final_file: str, top_n: int = 10):
    """显示最终分析结果"""
    logger = logging.getLogger(__name__)
    
    try:
        df = load_dataframe(get_data_path(final_file))
        if df is None:
            logger.warning("无法加载最终数据")
            return
        
        print("\n" + "=" * 80)
        print("小区房源数据分析结果")
        print("=" * 80)
        
        print(f"\n📊 数据概览:")
        print(f"   总小区数: {len(df)}")
        print(f"   总房源数: {df['housing_count'].sum() if 'housing_count' in df.columns else 'N/A'}")
        
        # 价格分析
        if 'avg_unit_price' in df.columns:
            valid_prices = df['avg_unit_price'].dropna()
            if len(valid_prices) > 0:
                print(f"\n💰 小区均价分析:")
                print(f"   整体平均单价: {valid_prices.mean():.0f} 元/平米")
                print(f"   单价中位数: {valid_prices.median():.0f} 元/平米")
                print(f"   单价范围: {valid_prices.min():.0f} - {valid_prices.max():.0f} 元/平米")
                
                # 价格等级分布
                if 'price_level' in df.columns:
                    price_dist = df['price_level'].value_counts()
                    print(f"   价格等级分布:")
                    for level, count in price_dist.items():
                        print(f"     {level}: {count} 个小区")
        
        # POI便利度分析
        if 'poi_convenience_index' in df.columns:
            valid_poi = df['poi_convenience_index'].dropna()
            if len(valid_poi) > 0:
                print(f"\n🏪 POI便利度分析:")
                print(f"   平均便利度指数: {valid_poi.mean():.1f}")
                print(f"   便利度中位数: {valid_poi.median():.1f}")
                print(f"   便利度范围: {valid_poi.min():.1f} - {valid_poi.max():.1f}")
                
                # 便利度等级分布
                if 'poi_convenience_level' in df.columns:
                    poi_dist = df['poi_convenience_level'].value_counts()
                    print(f"   便利度等级分布:")
                    for level, count in poi_dist.items():
                        print(f"     {level}: {count} 个小区")
        
        # 显示TOP小区
        show_top_communities(df, top_n)
        
    except Exception as e:
        logger.error(f"显示最终分析失败: {e}")

def show_top_communities(df: pd.DataFrame, top_n: int = 10):
    """显示TOP小区"""
    print(f"\n🏆 综合评价TOP {top_n}小区:")
    print("-" * 80)
    
    # 计算综合得分
    score_components = []
    
    # 价格得分（价格越低得分越高）
    if 'avg_unit_price' in df.columns:
        valid_prices = df['avg_unit_price'].dropna()
        if len(valid_prices) > 0:
            max_price = valid_prices.max()
            min_price = valid_prices.min()
            if max_price > min_price:
                price_score = 100 * (max_price - df['avg_unit_price']) / (max_price - min_price)
                score_components.append(('price_score', price_score, 0.4))
    
    # POI便利度得分
    if 'poi_convenience_index' in df.columns:
        poi_score = df['poi_convenience_index'].fillna(0)
        score_components.append(('poi_score', poi_score, 0.4))
    
    # 距离得分（距离越近得分越高）
    if 'distance_to_cbd_km' in df.columns:
        valid_distances = df['distance_to_cbd_km'].dropna()
        if len(valid_distances) > 0:
            max_distance = valid_distances.max()
            min_distance = valid_distances.min()
            if max_distance > min_distance:
                distance_score = 100 * (max_distance - df['distance_to_cbd_km']) / (max_distance - min_distance)
                score_components.append(('distance_score', distance_score, 0.2))
    
    # 计算综合得分
    if score_components:
        comprehensive_score = pd.Series([0.0] * len(df), index=df.index)
        for name, score, weight in score_components:
            comprehensive_score += score.fillna(0) * weight
        
        df['comprehensive_score'] = comprehensive_score
        
        # 排序并显示TOP小区
        top_communities = df.nlargest(top_n, 'comprehensive_score')
        
        for idx, (_, row) in enumerate(top_communities.iterrows(), 1):
            print(f"\n{idx}. {row['community_name']}")
            print(f"   综合得分: {row['comprehensive_score']:.1f}")
            
            if 'avg_unit_price' in row and pd.notna(row['avg_unit_price']):
                print(f"   平均单价: {row['avg_unit_price']:.0f} 元/平米")
            
            if 'housing_count' in row:
                print(f"   房源数量: {row['housing_count']} 套")
            
            if 'poi_convenience_index' in row and pd.notna(row['poi_convenience_index']):
                print(f"   便利度指数: {row['poi_convenience_index']:.1f}")
            
            if 'distance_to_cbd_km' in row and pd.notna(row['distance_to_cbd_km']):
                print(f"   距离CBD: {row['distance_to_cbd_km']:.1f} 公里")
    else:
        print("   无法计算综合得分，数据不足")

if __name__ == "__main__":
    sys.exit(main())
