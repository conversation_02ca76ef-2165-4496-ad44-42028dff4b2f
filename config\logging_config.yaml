version: 1
disable_existing_loggers: False

formatters:
  standard:
    format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  detailed:
    format: "%(asctime)s - %(name)s - %(levelname)s - %(module)s - %(funcName)s - %(message)s"

handlers:
  console:
    class: logging.StreamHandler
    level: INFO
    formatter: standard
    stream: ext://sys.stdout

  file:
    class: logging.FileHandler
    level: DEBUG
    formatter: detailed
    filename: logs/cbd_analysis.log
    mode: a

  error_file:
    class: logging.FileHandler
    level: ERROR
    formatter: detailed
    filename: logs/cbd_analysis_errors.log
    mode: a

loggers:
  "":
    level: DEBUG
    handlers: [console, file, error_file]
    propagate: False

  requests:
    level: WARNING
    handlers: [console, file]
    propagate: False

  urllib3:
    level: WARNING
    handlers: [console, file]
    propagate: False
