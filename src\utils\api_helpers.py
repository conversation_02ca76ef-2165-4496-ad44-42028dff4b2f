"""
高德地图API封装工具
提供地理编码、POI搜索、路径规划等功能
"""

import requests
import time
import json
import logging
from typing import Dict, List, Tuple, Optional, Union
from .config_loader import config

logger = logging.getLogger(__name__)

# 高德API端点
GAODE_GEOCODE_URL = "https://restapi.amap.com/v3/geocode/geo"
GAODE_REGEOCODE_URL = "https://restapi.amap.com/v3/geocode/regeo"
GAODE_POI_AROUND_URL = "https://restapi.amap.com/v3/place/around"
GAODE_TRANSIT_URL = "https://restapi.amap.com/v3/direction/transit/integrated"

class GaodeAPIError(Exception):
    """高德API异常类"""
    pass

def call_gaode_api(url: str, params: Dict, api_key_config_name: str = "gaode_ak",
                   max_retries: int = 3, delay: float = 0.5) -> Optional[Dict]:
    """
    通用高德API调用函数
    
    Args:
        url (str): API端点URL
        params (dict): 请求参数
        api_key_config_name (str): API密钥配置名
        max_retries (int): 最大重试次数
        delay (float): 请求间隔（秒）
        
    Returns:
        dict: API响应数据，失败返回None
        
    Raises:
        GaodeAPIError: API调用失败
    """
    # 获取API密钥
    api_key = config.get("API_KEYS", api_key_config_name)
    if not api_key or api_key == "YOUR_GAODE_API_KEY_HERE":
        raise GaodeAPIError("高德API密钥未配置或无效")
    
    # 设置通用参数
    params.update({
        'key': api_key,
        'output': 'JSON'
    })
    
    for attempt in range(max_retries):
        try:
            # 添加延时以控制QPS
            if delay > 0:
                time.sleep(delay)
            
            logger.debug(f"调用高德API: {url}, 参数: {params}")
            response = requests.get(url, params=params, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            
            # 检查高德API返回状态
            if data.get('status') != '1':
                error_msg = data.get('info', '未知错误')
                logger.error(f"高德API返回错误: {error_msg}")
                if attempt == max_retries - 1:
                    raise GaodeAPIError(f"高德API错误: {error_msg}")
                continue
            
            logger.debug(f"API调用成功，返回数据量: {len(str(data))}")
            return data
            
        except requests.exceptions.SSLError as e:
            logger.warning(f"SSL连接失败 (尝试 {attempt + 1}/{max_retries}): {e}")
            if attempt == max_retries - 1:
                logger.error("SSL握手失败，可能是网络环境问题，请检查网络连接或代理设置")
                raise GaodeAPIError(f"SSL连接失败: {e}")
            time.sleep(2)  # SSL错误时等待更长时间
        except requests.exceptions.RequestException as e:
            logger.warning(f"网络请求失败 (尝试 {attempt + 1}/{max_retries}): {e}")
            if attempt == max_retries - 1:
                raise GaodeAPIError(f"网络请求失败: {e}")
            time.sleep(1)  # 重试前等待
        except json.JSONDecodeError as e:
            logger.error(f"JSON解析失败: {e}")
            if attempt == max_retries - 1:
                raise GaodeAPIError(f"响应格式错误: {e}")
        except Exception as e:
            logger.error(f"API调用异常: {e}")
            if attempt == max_retries - 1:
                raise GaodeAPIError(f"API调用异常: {e}")
    
    return None

def clean_address_for_geocoding(address: str) -> str:
    """
    清洗地址字符串，去除可能导致API错误的字符

    Args:
        address (str): 原始地址

    Returns:
        str: 清洗后的地址
    """
    if not address or address.strip() == '':
        return ''

    # 转换为字符串并去除首尾空格
    address = str(address).strip()

    # 去除常见的无效字符和描述词
    address = address.replace('官方核验新上近地铁精装', '')
    address = address.replace('公寓拎包入住精装押一付一随时看房', '')
    address = address.replace('官方核验', '')
    address = address.replace('新上', '')
    address = address.replace('近地铁', '')
    address = address.replace('精装', '')
    address = address.replace('拎包入住', '')
    address = address.replace('押一付一', '')
    address = address.replace('随时看房', '')

    # 去除括号及其内容
    import re
    address = re.sub(r'[（(].*?[）)]', '', address)

    # 去除多余的空格
    address = re.sub(r'\s+', ' ', address).strip()

    # 如果地址太短或包含无效内容，返回空字符串
    if len(address) < 2 or address in ['nan', 'None', '未知', '暂无']:
        return ''

    return address

def geocode_address_gaode(address: str, city: str = "上海") -> Optional[Tuple[float, float]]:
    """
    使用高德API进行地理编码

    Args:
        address (str): 地址
        city (str): 城市名称

    Returns:
        tuple: (经度, 纬度) 或 None
    """
    # 清洗地址
    cleaned_address = clean_address_for_geocoding(address)
    if not cleaned_address:
        logger.warning(f"地址清洗后为空，跳过: {address}")
        return None

    params = {
        'address': cleaned_address,
        'city': city
    }

    try:
        data = call_gaode_api(GAODE_GEOCODE_URL, params, delay=0.5)
        if not data:
            return None

        geocodes = data.get('geocodes', [])
        if not geocodes:
            logger.warning(f"地址未找到: {cleaned_address}")
            return None

        location = geocodes[0].get('location', '')
        if not location:
            return None

        # 解析经纬度 "lng,lat"
        lng, lat = map(float, location.split(','))
        logger.debug(f"地理编码成功: {cleaned_address} -> ({lng}, {lat})")
        return lng, lat

    except Exception as e:
        logger.error(f"地理编码失败: {cleaned_address}, 错误: {e}")
        return None

def search_poi_around_gaode(location_coords_str: str, poi_types_str: str,
                           radius: int = 1000, page_size: int = 25,
                           max_pages: int = 20) -> List[Dict]:
    """
    使用高德API搜索周边POI

    Args:
        location_coords_str (str): 中心点坐标 "lng,lat"
        poi_types_str (str): POI类型编码，多个用|分隔
        radius (int): 搜索半径（米）
        page_size (int): 每页数量
        max_pages (int): 最大页数

    Returns:
        list: POI列表
    """
    all_pois = []
    page = 1

    while page <= max_pages:
        params = {
            'location': location_coords_str,
            'types': poi_types_str,
            'radius': radius,
            'offset': page_size,
            'page': page,
            'extensions': 'all'  # 获取详细信息
        }

        try:
            data = call_gaode_api(GAODE_POI_AROUND_URL, params, delay=0.1)
            if not data:
                break

            pois = data.get('pois', [])
            if not pois:
                logger.debug(f"第{page}页无POI数据，停止搜索")
                break

            all_pois.extend(pois)
            logger.debug(f"获取第{page}页POI数据: {len(pois)}条")

            # 如果返回数量少于页面大小，说明已到最后一页
            if len(pois) < page_size:
                break

            page += 1

        except Exception as e:
            logger.error(f"POI搜索失败 (页面{page}): {e}")
            break

    logger.info(f"POI搜索完成，共获取 {len(all_pois)} 条数据")
    return all_pois

def get_transit_route_gaode(origin_coords_str: str, destination_coords_str: str,
                           city_code: str = "021", strategy: int = 0) -> Optional[Dict]:
    """
    使用高德API获取公交路径规划

    Args:
        origin_coords_str (str): 起点坐标 "lng,lat"
        destination_coords_str (str): 终点坐标 "lng,lat"
        city_code (str): 城市代码，上海为"021"
        strategy (int): 路径策略 0-最快捷, 1-最经济, 2-最少换乘, 3-最少步行, 5-不乘地铁

    Returns:
        dict: 路径信息 {'duration_min': float, 'transfers': int, 'walking_km': float} 或 None
    """
    params = {
        'origin': origin_coords_str,
        'destination': destination_coords_str,
        'city': city_code,
        'strategy': strategy
    }

    try:
        data = call_gaode_api(GAODE_TRANSIT_URL, params, delay=0.2)
        if not data:
            return None

        route = data.get('route', {})
        transits = route.get('transits', [])

        if not transits:
            logger.warning(f"未找到公交路径: {origin_coords_str} -> {destination_coords_str}")
            return None

        # 取第一个方案
        transit = transits[0]

        # 提取路径信息
        duration_seconds = int(transit.get('duration', 0))
        walking_distance = int(transit.get('walking_distance', 0))

        # 计算换乘次数
        segments = transit.get('segments', [])
        transfers = 0

        for segment in segments:
            bus = segment.get('bus', {})
            buslines = bus.get('buslines', [])
            if buslines:
                transfers += 1

        # 换乘次数 = 乘坐线路数 - 1
        transfers = max(0, transfers - 1)

        result = {
            'duration_min': duration_seconds / 60.0,
            'transfers': transfers,
            'walking_km': walking_distance / 1000.0
        }

        logger.debug(f"路径规划成功: {result}")
        return result

    except Exception as e:
        logger.error(f"路径规划失败: {origin_coords_str} -> {destination_coords_str}, 错误: {e}")
        return None

def batch_geocode_addresses(addresses: List[str], city: str = "上海",
                           delay: float = 1.0) -> List[Optional[Tuple[float, float]]]:
    """
    批量地理编码

    Args:
        addresses (list): 地址列表
        city (str): 城市名称
        delay (float): 请求间隔

    Returns:
        list: 坐标列表，失败的为None
    """
    results = []
    total = len(addresses)
    failed_addresses = []

    logger.info(f"开始批量地理编码，共 {total} 个地址")

    for i, address in enumerate(addresses, 1):
        if i % 10 == 0:
            logger.info(f"地理编码进度: {i}/{total}")

        try:
            coords = geocode_address_gaode(address, city)
            results.append(coords)

            if coords is None:
                failed_addresses.append(address)

        except Exception as e:
            logger.error(f"地理编码异常 (地址: {address}): {e}")
            results.append(None)
            failed_addresses.append(address)

        # 控制请求频率
        if delay > 0:
            time.sleep(delay)

    success_count = sum(1 for r in results if r is not None)
    logger.info(f"批量地理编码完成，成功: {success_count}/{total}")

    if failed_addresses:
        logger.warning(f"失败的地址数量: {len(failed_addresses)}")
        logger.debug(f"失败的地址示例: {failed_addresses[:5]}")

    return results
