#!/usr/bin/env python3
"""
基于小区的陆家嘴房产分析主程序
实施分小区的方法：从链家爬取小区数据，收集POI信息，计算综合评分
"""

import logging
import sys
import argparse
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.utils.config_loader import config
from src.utils.logger_setup import setup_logger
from src.data_collection.community_collector import CommunityCollector
from src.data_collection.community_poi_collector import CommunityPOICollector
from src.data_collection.community_commute_calculator import CommunityCommuteCalculator
from src.analysis.community_scoring_system import CommunityScoringSystem

def main():
    """主程序入口"""
    parser = argparse.ArgumentParser(description='陆家嘴小区综合分析系统')
    parser.add_argument('--step', choices=['all', 'community', 'poi', 'commute', 'score'], 
                       default='all', help='执行步骤')
    parser.add_argument('--community-file', help='小区数据文件路径')
    parser.add_argument('--poi-file', help='POI数据文件路径')
    parser.add_argument('--commute-file', help='通勤数据文件路径')
    parser.add_argument('--top-n', type=int, default=10, help='显示前N个小区')
    
    args = parser.parse_args()
    
    # 设置日志
    setup_logger()
    logger = logging.getLogger(__name__)
    
    logger.info("=" * 60)
    logger.info("陆家嘴小区综合分析系统启动")
    logger.info("=" * 60)
    
    try:
        if args.step in ['all', 'community']:
            logger.info("步骤1: 收集小区数据")
            community_collector = CommunityCollector()
            community_file = community_collector.collect_community_data()
            
            if not community_file:
                logger.error("小区数据收集失败")
                return 1
            
            # 显示小区摘要
            summary = community_collector.get_community_summary(community_file)
            if summary:
                logger.info(f"小区数据摘要: {summary}")
        else:
            community_file = args.community_file
        
        if args.step in ['all', 'poi']:
            logger.info("步骤2: 收集小区POI数据")
            poi_collector = CommunityPOICollector()
            poi_file = poi_collector.collect_community_poi_data(community_file)
            
            if not poi_file:
                logger.error("POI数据收集失败")
                return 1
        else:
            poi_file = args.poi_file
        
        if args.step in ['all', 'commute']:
            logger.info("步骤3: 计算小区通勤数据")
            commute_calculator = CommunityCommuteCalculator()
            commute_file = commute_calculator.calculate_community_commute_scores(community_file)
            
            if not commute_file:
                logger.error("通勤数据计算失败")
                return 1
            
            # 显示通勤摘要
            summary = commute_calculator.get_commute_summary(commute_file)
            if summary:
                logger.info(f"通勤数据摘要: {summary}")
        else:
            commute_file = args.commute_file
        
        if args.step in ['all', 'score']:
            logger.info("步骤4: 计算综合评分")
            scoring_system = CommunityScoringSystem()
            score_file = scoring_system.calculate_comprehensive_scores(
                community_file, poi_file, commute_file
            )
            
            if not score_file:
                logger.error("综合评分计算失败")
                return 1
            
            # 显示顶级小区
            logger.info(f"前{args.top_n}名小区:")
            top_communities = scoring_system.get_top_communities(score_file, args.top_n)
            
            if not top_communities.empty:
                print("\n" + "=" * 80)
                print(f"陆家嘴商圈前{args.top_n}名小区综合评分")
                print("=" * 80)
                
                for idx, row in top_communities.iterrows():
                    print(f"\n{int(row['comprehensive_rank'])}. {row['community_name']}")
                    print(f"   综合得分: {row['comprehensive_score']:.1f}")
                    print(f"   等级: {row['comprehensive_level']}")
                    
                    if 'distance_to_cbd_km' in row:
                        print(f"   距离CBD: {row['distance_to_cbd_km']:.1f}公里")
                    
                    if 'commute_score' in row:
                        print(f"   通勤得分: {row['commute_score']:.1f}")
                    
                    if 'poi_score' in row:
                        print(f"   生活便利度: {row['poi_score']:.1f}")
                    
                    if 'recommendation' in row and row['recommendation']:
                        print(f"   推荐标签: {row['recommendation']}")
                
                print("\n" + "=" * 80)
            else:
                logger.warning("未能获取顶级小区数据")
        
        logger.info("=" * 60)
        logger.info("陆家嘴小区综合分析完成")
        logger.info("=" * 60)
        
        return 0
        
    except KeyboardInterrupt:
        logger.info("用户中断程序")
        return 1
    except Exception as e:
        logger.error(f"程序执行失败: {e}", exc_info=True)
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
