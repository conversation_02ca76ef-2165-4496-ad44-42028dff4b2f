"""
文件I/O工具
提供数据读写、路径管理等功能，支持文件和数据库存储
"""

import pandas as pd
import json
import os
import logging
from pathlib import Path
from typing import Dict, List, Any, Optional

logger = logging.getLogger(__name__)

def ensure_dir(path: str) -> None:
    """确保目录存在"""
    Path(path).parent.mkdir(parents=True, exist_ok=True)

def save_dataframe(df: pd.DataFrame, filepath: str, index: bool = False) -> None:
    """
    保存DataFrame到CSV文件
    
    Args:
        df: DataFrame对象
        filepath: 文件路径
        index: 是否保存索引
    """
    ensure_dir(filepath)
    df.to_csv(filepath, index=index, encoding='utf-8-sig')
    logger.info(f"数据已保存到: {filepath}, 形状: {df.shape}")

def load_dataframe(filepath: str) -> Optional[pd.DataFrame]:
    """
    从CSV文件加载DataFrame
    
    Args:
        filepath: 文件路径
        
    Returns:
        DataFrame对象或None
    """
    if not os.path.exists(filepath):
        logger.warning(f"文件不存在: {filepath}")
        return None
    
    try:
        df = pd.read_csv(filepath, encoding='utf-8-sig')
        logger.info(f"数据已加载: {filepath}, 形状: {df.shape}")
        return df
    except Exception as e:
        logger.error(f"加载数据失败: {filepath}, 错误: {e}")
        return None

def save_json(data: Any, filepath: str, indent: int = 2) -> None:
    """
    保存数据到JSON文件
    
    Args:
        data: 要保存的数据
        filepath: 文件路径
        indent: 缩进空格数
    """
    ensure_dir(filepath)
    with open(filepath, 'w', encoding='utf-8') as f:
        json.dump(data, f, ensure_ascii=False, indent=indent)
    logger.info(f"JSON数据已保存到: {filepath}")

def load_json(filepath: str) -> Optional[Any]:
    """
    从JSON文件加载数据
    
    Args:
        filepath: 文件路径
        
    Returns:
        数据对象或None
    """
    if not os.path.exists(filepath):
        logger.warning(f"文件不存在: {filepath}")
        return None
    
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            data = json.load(f)
        logger.info(f"JSON数据已加载: {filepath}")
        return data
    except Exception as e:
        logger.error(f"加载JSON失败: {filepath}, 错误: {e}")
        return None

def get_project_root() -> Path:
    """获取项目根目录"""
    return Path(__file__).parent.parent.parent

def get_data_path(relative_path: str) -> str:
    """
    获取数据文件的完整路径

    Args:
        relative_path: 相对于项目根目录的路径

    Returns:
        完整路径
    """
    return str(get_project_root() / relative_path)

# 数据库相关函数
def save_dataframe_to_db(df: pd.DataFrame, table_name: str, if_exists: str = 'append') -> bool:
    """
    保存DataFrame到数据库

    Args:
        df: DataFrame对象
        table_name: 数据库表名
        if_exists: 如果表存在的处理方式 ('fail', 'replace', 'append')

    Returns:
        bool: 是否成功
    """
    try:
        from .database import db_manager
        return db_manager.save_dataframe(df, table_name, if_exists)
    except Exception as e:
        logger.error(f"保存数据到数据库失败: {e}")
        return False

def load_dataframe_from_db(table_name: str, where_clause: str = None) -> Optional[pd.DataFrame]:
    """
    从数据库加载DataFrame

    Args:
        table_name: 数据库表名
        where_clause: WHERE条件子句

    Returns:
        DataFrame对象或None
    """
    try:
        from .database import db_manager
        return db_manager.load_dataframe(table_name, where_clause)
    except Exception as e:
        logger.error(f"从数据库加载数据失败: {e}")
        return None

def save_dataframe_hybrid(df: pd.DataFrame, filepath: str = None, table_name: str = None,
                         if_exists: str = 'append', index: bool = False) -> bool:
    """
    混合保存DataFrame到文件和数据库

    Args:
        df: DataFrame对象
        filepath: 文件路径（可选）
        table_name: 数据库表名（可选）
        if_exists: 如果表存在的处理方式
        index: 是否保存索引到文件

    Returns:
        bool: 是否成功
    """
    success = True

    # 保存到文件
    if filepath:
        try:
            save_dataframe(df, filepath, index)
        except Exception as e:
            logger.error(f"保存到文件失败: {e}")
            success = False

    # 保存到数据库
    if table_name:
        try:
            if not save_dataframe_to_db(df, table_name, if_exists):
                success = False
        except Exception as e:
            logger.error(f"保存到数据库失败: {e}")
            success = False

    return success

def load_dataframe_hybrid(filepath: str = None, table_name: str = None,
                         where_clause: str = None, prefer_db: bool = True) -> Optional[pd.DataFrame]:
    """
    混合从文件或数据库加载DataFrame

    Args:
        filepath: 文件路径（可选）
        table_name: 数据库表名（可选）
        where_clause: WHERE条件子句
        prefer_db: 优先使用数据库

    Returns:
        DataFrame对象或None
    """
    df = None

    if prefer_db and table_name:
        # 优先从数据库加载
        df = load_dataframe_from_db(table_name, where_clause)
        if df is not None:
            logger.info(f"从数据库表 {table_name} 加载数据成功")
            return df

    if filepath:
        # 从文件加载
        df = load_dataframe(filepath)
        if df is not None:
            logger.info(f"从文件 {filepath} 加载数据成功")
            return df

    if not prefer_db and table_name:
        # 最后尝试从数据库加载
        df = load_dataframe_from_db(table_name, where_clause)
        if df is not None:
            logger.info(f"从数据库表 {table_name} 加载数据成功")
            return df

    logger.warning("无法从任何数据源加载数据")
    return None

def list_files(directory: str, pattern: str = "*") -> List[str]:
    """
    列出目录中的文件
    
    Args:
        directory: 目录路径
        pattern: 文件模式
        
    Returns:
        文件路径列表
    """
    path = Path(directory)
    if not path.exists():
        logger.warning(f"目录不存在: {directory}")
        return []
    
    files = list(path.glob(pattern))
    return [str(f) for f in files if f.is_file()]

def backup_file(filepath: str) -> str:
    """
    备份文件
    
    Args:
        filepath: 原文件路径
        
    Returns:
        备份文件路径
    """
    if not os.path.exists(filepath):
        logger.warning(f"文件不存在，无法备份: {filepath}")
        return ""
    
    backup_path = f"{filepath}.backup"
    import shutil
    shutil.copy2(filepath, backup_path)
    logger.info(f"文件已备份: {filepath} -> {backup_path}")
    return backup_path
