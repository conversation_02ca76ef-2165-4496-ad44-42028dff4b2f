"""
基于小区的通勤计算器
计算每个小区到CBD的通勤便利性
"""

import pandas as pd
import numpy as np
import logging
from typing import Dict, Optional
from tqdm import tqdm
from ..utils.config_loader import config
from ..utils.api_helpers import get_transit_route_gaode
from ..utils.file_io import load_dataframe, save_dataframe, get_data_path

logger = logging.getLogger(__name__)

class CommunityCommuteCalculator:
    """基于小区的通勤计算器"""
    
    def __init__(self):
        # CBD坐标
        self.cbd_lat = config.getfloat("CBD_INFO", "latitude", 31.239638)
        self.cbd_lng = config.getfloat("CBD_INFO", "longitude", 121.499767)
        self.cbd_coords = f"{self.cbd_lng},{self.cbd_lat}"
        
        # 路径策略
        self.strategy = config.getint("SCRAPER_PARAMS", "commute_strategy_gaode", 0)
        
        logger.info(f"CBD坐标: {self.cbd_coords}")
        logger.info(f"路径策略: {self.strategy}")
    
    def calculate_community_commute_scores(self, community_file: str = None) -> str:
        """
        计算所有小区到CBD的通勤评分
        
        Args:
            community_file: 小区数据文件路径
            
        Returns:
            保存的通勤数据文件路径
        """
        # 加载小区数据
        if not community_file:
            community_file = config.get("PATHS", "raw_housing_data",
                                      "data/raw/communities_lujiazui.csv")
        
        community_df = load_dataframe(get_data_path(community_file))
        if community_df is None:
            logger.error(f"无法加载小区数据: {community_file}")
            return ""
        
        # 只处理有坐标的小区
        valid_communities = community_df[
            (community_df['geocoded'] == True) & 
            community_df['longitude'].notna() & 
            community_df['latitude'].notna()
        ].copy()
        
        logger.info(f"开始计算 {len(valid_communities)} 个小区的通勤数据")
        
        # 计算通勤数据
        commute_data = []
        
        for idx, row in tqdm(valid_communities.iterrows(), total=len(valid_communities), desc="计算通勤数据"):
            try:
                commute_record = self._calculate_single_community_commute(row)
                if commute_record:
                    commute_data.append(commute_record)
                    
            except Exception as e:
                logger.error(f"计算小区通勤失败 ({row['community_name']}): {e}")
                continue
        
        # 转换为DataFrame
        commute_df = pd.DataFrame(commute_data)
        
        # 计算通勤便捷度指数
        commute_df = self._calculate_commute_convenience_index(commute_df)
        
        # 保存通勤数据
        output_file = config.get("PATHS", "processed_commute_data",
                                "data/processed/community_commute_scores.csv")
        output_path = get_data_path(output_file)
        
        save_dataframe(commute_df, output_path)
        
        logger.info(f"小区通勤数据计算完成: {len(commute_df)} 个小区")
        logger.info(f"数据已保存到: {output_path}")
        
        # 输出统计信息
        self._print_commute_stats(commute_df)
        
        return output_path
    
    def _calculate_single_community_commute(self, community_row) -> Optional[Dict]:
        """计算单个小区的通勤数据"""
        community_name = community_row['community_name']
        longitude = community_row['longitude']
        latitude = community_row['latitude']
        
        # 构建起点坐标
        origin_coords = f"{longitude},{latitude}"
        
        logger.debug(f"计算小区 {community_name} 的通勤数据")
        
        # 计算通勤路径
        route_info = get_transit_route_gaode(
            origin_coords,
            self.cbd_coords,
            city_code="021",
            strategy=self.strategy
        )
        
        # 构建通勤数据记录
        commute_record = {
            'community_name': community_name,
            'longitude': longitude,
            'latitude': latitude,
            'cbd_longitude': self.cbd_lng,
            'cbd_latitude': self.cbd_lat,
            'distance_to_cbd_km': community_row.get('distance_to_cbd_km')
        }
        
        if route_info:
            commute_record.update({
                'commute_duration_min': route_info['duration_min'],
                'commute_transfers': route_info['transfers'],
                'commute_walking_km': route_info['walking_km'],
                'route_found': True
            })
            logger.debug(f"通勤计算成功: {community_name} - {route_info['duration_min']:.1f}分钟")
        else:
            # 如果没有找到路径，使用直线距离估算
            estimated_time = self._estimate_commute_time(community_row.get('distance_to_cbd_km', 0))
            commute_record.update({
                'commute_duration_min': estimated_time,
                'commute_transfers': None,
                'commute_walking_km': None,
                'route_found': False
            })
            logger.warning(f"未找到通勤路径，使用估算: {community_name} - {estimated_time:.1f}分钟")
        
        # 添加延时避免API限流
        import time
        time.sleep(0.5)
        
        return commute_record
    
    def _estimate_commute_time(self, distance_km: float) -> float:
        """基于直线距离估算通勤时间"""
        if distance_km <= 0:
            return 30  # 默认30分钟
        
        # 简单估算：假设平均速度20公里/小时，加上等车和换乘时间
        base_time = distance_km / 20 * 60  # 基础行驶时间（分钟）
        transfer_time = min(distance_km / 5, 4) * 5  # 换乘等待时间
        
        return base_time + transfer_time + 10  # 加上起始等车时间
    
    def _calculate_commute_convenience_index(self, df: pd.DataFrame) -> pd.DataFrame:
        """计算通勤便捷度指数"""
        logger.info("计算通勤便捷度指数...")
        
        # 通勤时间得分（时间越短得分越高）
        if 'commute_duration_min' in df.columns:
            valid_commute = df['commute_duration_min'].notna()
            
            if valid_commute.sum() > 0:
                # 使用反向对数函数计算得分
                max_duration = df.loc[valid_commute, 'commute_duration_min'].max()
                min_duration = df.loc[valid_commute, 'commute_duration_min'].min()
                
                time_score = pd.Series([np.nan] * len(df), index=df.index)
                
                if max_duration > min_duration:
                    # 时间得分：线性反向映射到0-100
                    time_score.loc[valid_commute] = 100 * (
                        (max_duration - df.loc[valid_commute, 'commute_duration_min']) / 
                        (max_duration - min_duration)
                    )
                else:
                    time_score.loc[valid_commute] = 100
                
                df['commute_time_score'] = time_score
        
        # 换乘便利度得分
        if 'commute_transfers' in df.columns:
            transfer_score = pd.Series([np.nan] * len(df), index=df.index)
            valid_transfers = df['commute_transfers'].notna()
            
            if valid_transfers.sum() > 0:
                # 换乘次数得分映射
                transfer_mapping = {0: 100, 1: 85, 2: 70, 3: 55, 4: 40}
                
                for idx in df[valid_transfers].index:
                    transfers = int(df.loc[idx, 'commute_transfers'])
                    transfer_score.loc[idx] = transfer_mapping.get(
                        transfers, max(0, 40 - (transfers - 4) * 10)
                    )
                
                df['commute_transfer_score'] = transfer_score
        
        # 步行距离得分
        if 'commute_walking_km' in df.columns:
            walking_score = pd.Series([np.nan] * len(df), index=df.index)
            valid_walking = df['commute_walking_km'].notna()
            
            if valid_walking.sum() > 0:
                # 步行距离越短得分越高
                max_walking = df.loc[valid_walking, 'commute_walking_km'].max()
                min_walking = df.loc[valid_walking, 'commute_walking_km'].min()
                
                if max_walking > min_walking:
                    walking_score.loc[valid_walking] = 100 * (
                        (max_walking - df.loc[valid_walking, 'commute_walking_km']) / 
                        (max_walking - min_walking)
                    )
                else:
                    walking_score.loc[valid_walking] = 100
                
                df['commute_walking_score'] = walking_score
        
        # 综合通勤便捷度指数
        commute_index = pd.Series([np.nan] * len(df), index=df.index)
        
        score_columns = ['commute_time_score', 'commute_transfer_score', 'commute_walking_score']
        weights = [0.6, 0.3, 0.1]  # 时间权重最高
        
        for idx in df.index:
            scores = []
            valid_weights = []
            
            for col, weight in zip(score_columns, weights):
                if col in df.columns and pd.notna(df.loc[idx, col]):
                    scores.append(df.loc[idx, col])
                    valid_weights.append(weight)
            
            if scores:
                # 标准化权重
                total_weight = sum(valid_weights)
                if total_weight > 0:
                    weighted_score = sum(s * w for s, w in zip(scores, valid_weights)) / total_weight
                    commute_index.loc[idx] = weighted_score
        
        df['commute_convenience_index'] = commute_index
        
        # 通勤便捷度等级
        df['commute_convenience_level'] = pd.cut(
            df['commute_convenience_index'],
            bins=[0, 20, 40, 60, 80, 100],
            labels=['很不便', '不便', '一般', '便利', '很便利']
        )
        
        # 通勤时间分类
        if 'commute_duration_min' in df.columns:
            df['commute_time_category'] = pd.cut(
                df['commute_duration_min'],
                bins=[0, 20, 30, 45, 60, float('inf')],
                labels=['很快', '较快', '适中', '较慢', '很慢']
            )
        
        valid_commute_index = df['commute_convenience_index'].notna()
        if valid_commute_index.sum() > 0:
            avg_score = df.loc[valid_commute_index, 'commute_convenience_index'].mean()
            logger.info(f"通勤便捷度指数计算完成，平均得分: {avg_score:.1f}")
        
        return df
    
    def _print_commute_stats(self, commute_df: pd.DataFrame):
        """打印通勤统计信息"""
        logger.info("通勤数据统计:")
        
        total_count = len(commute_df)
        valid_routes = commute_df['route_found'].sum() if 'route_found' in commute_df.columns else 0
        
        logger.info(f"  总小区数: {total_count}")
        logger.info(f"  有效路径: {valid_routes} ({valid_routes/total_count*100:.1f}%)")
        
        if valid_routes > 0:
            valid_df = commute_df[commute_df['route_found'] == True]
            
            if 'commute_duration_min' in valid_df.columns:
                avg_duration = valid_df['commute_duration_min'].mean()
                min_duration = valid_df['commute_duration_min'].min()
                max_duration = valid_df['commute_duration_min'].max()
                
                logger.info(f"  平均通勤时间: {avg_duration:.1f} 分钟")
                logger.info(f"  最短通勤时间: {min_duration:.1f} 分钟")
                logger.info(f"  最长通勤时间: {max_duration:.1f} 分钟")
            
            if 'commute_transfers' in valid_df.columns:
                avg_transfers = valid_df['commute_transfers'].mean()
                logger.info(f"  平均换乘次数: {avg_transfers:.1f} 次")
        
        if 'commute_convenience_index' in commute_df.columns:
            valid_index = commute_df['commute_convenience_index'].notna()
            if valid_index.sum() > 0:
                avg_index = commute_df.loc[valid_index, 'commute_convenience_index'].mean()
                logger.info(f"  平均通勤便捷度: {avg_index:.1f}")
    
    def get_commute_summary(self, commute_file: str = None) -> Dict:
        """获取通勤数据摘要"""
        if not commute_file:
            commute_file = config.get("PATHS", "processed_commute_data",
                                    "data/processed/community_commute_scores.csv")
        
        try:
            commute_df = load_dataframe(get_data_path(commute_file))
            if commute_df is None:
                return {}
            
            summary = {
                'total_communities': len(commute_df),
                'successful_routes': commute_df['route_found'].sum() if 'route_found' in commute_df.columns else 0,
                'avg_commute_time': commute_df['commute_duration_min'].mean() if 'commute_duration_min' in commute_df.columns else 0,
                'avg_convenience_index': commute_df['commute_convenience_index'].mean() if 'commute_convenience_index' in commute_df.columns else 0
            }
            
            return summary
            
        except Exception as e:
            logger.error(f"获取通勤摘要失败: {e}")
            return {}
