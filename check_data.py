#!/usr/bin/env python3
"""
检查数据分布
"""

import pandas as pd
import os

def check_raw_data():
    """检查原始数据"""
    print("检查原始数据...")
    raw_dir = "data/raw"
    
    if not os.path.exists(raw_dir):
        print("原始数据目录不存在")
        return
    
    files = [f for f in os.listdir(raw_dir) if f.startswith('lianjia_housing_selenium_') and f.endswith('.csv')]
    
    if not files:
        print("未找到原始数据文件")
        return
    
    # 获取最新文件
    latest = max(files, key=lambda x: os.path.getmtime(os.path.join(raw_dir, x)))
    filepath = os.path.join(raw_dir, latest)
    
    print(f"最新文件: {latest}")
    
    try:
        df = pd.read_csv(filepath, encoding='utf-8-sig')
        print(f"总记录数: {len(df)}")
        print(f"字段: {list(df.columns)}")
        
        if 'community' in df.columns:
            community_counts = df['community'].value_counts()
            print(f"总小区数: {df['community'].nunique()}")
            print("小区分布（前10）:")
            print(community_counts.head(10))
            
            # 统计数据量≥5的小区
            valid_communities = community_counts[community_counts >= 5]
            print(f"数据量≥5的小区数: {len(valid_communities)}")
            if len(valid_communities) > 0:
                print("数据量≥5的小区:")
                print(valid_communities)
        
    except Exception as e:
        print(f"读取文件失败: {e}")

def check_processed_data():
    """检查处理后的数据"""
    print("\n检查处理后的数据...")
    filepath = "data/processed/housing_cleaned_geocoded.csv"
    
    if not os.path.exists(filepath):
        print("处理后的数据文件不存在")
        return
    
    try:
        df = pd.read_csv(filepath, encoding='utf-8-sig')
        print(f"总记录数: {len(df)}")
        print(f"字段: {list(df.columns)}")
        
        if 'community' in df.columns:
            community_counts = df['community'].value_counts()
            print(f"总小区数: {df['community'].nunique()}")
            print("小区分布（前10）:")
            print(community_counts.head(10))
            
            # 统计数据量≥5的小区
            valid_communities = community_counts[community_counts >= 5]
            print(f"数据量≥5的小区数: {len(valid_communities)}")
            if len(valid_communities) > 0:
                print("数据量≥5的小区:")
                print(valid_communities)
        
    except Exception as e:
        print(f"读取文件失败: {e}")

if __name__ == "__main__":
    check_raw_data()
    check_processed_data()
