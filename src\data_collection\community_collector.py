"""
小区数据收集器
专门收集陆家嘴商圈内的小区信息
"""

import pandas as pd
import numpy as np
import logging
import re
from typing import List, Dict, Set
from urllib.parse import urljoin
from .base_scraper import BaseScraper
from ..utils.config_loader import config
from ..utils.file_io import save_dataframe, get_data_path
from ..utils.api_helpers import geocode_address_gaode, batch_geocode_addresses

logger = logging.getLogger(__name__)

class CommunityCollector(BaseScraper):
    """小区数据收集器"""
    
    def __init__(self):
        delay = config.getfloat("SCRAPER_PARAMS", "delay_between_requests", 1.0)
        super().__init__(delay=delay)
        
        self.base_url = config.get("SCRAPER_PARAMS", "lianjia_base_url", 
                                  "https://sh.lianjia.com/ershoufang/pudong/")
        self.max_pages = config.getint("SCRAPER_PARAMS", "max_pages", 20)
        
    def collect_communities_from_listings(self) -> Set[str]:
        """从房产列表页面收集小区名称"""
        logger.info("从房产列表收集小区信息...")
        
        communities = set()
        
        for page in range(1, self.max_pages + 1):
            logger.info(f"收集第 {page} 页小区信息")
            
            # 构建页面URL
            if page == 1:
                page_url = self.base_url
            else:
                page_url = f"{self.base_url}pg{page}/"
            
            # 获取页面
            soup = self.get_page(page_url)
            if not soup:
                logger.warning(f"无法获取第 {page} 页，跳过")
                continue
            
            # 解析小区信息
            page_communities = self._extract_communities_from_page(soup)
            communities.update(page_communities)
            
            logger.info(f"第 {page} 页发现 {len(page_communities)} 个小区")
            
            # 检查是否有下一页
            if not self._has_next_page(soup):
                logger.info("已到最后一页")
                break
        
        logger.info(f"总共收集到 {len(communities)} 个不同的小区")
        return communities
    
    def _extract_communities_from_page(self, soup) -> Set[str]:
        """从页面中提取小区名称"""
        communities = set()
        
        # 查找房产列表项
        house_items = soup.find_all('li', class_='clear')
        
        for item in house_items:
            try:
                # 从位置信息中提取小区名
                position_elem = item.find('div', class_='positionInfo')
                if position_elem:
                    position_text = self.extract_text(position_elem)
                    community = self._extract_community_name(position_text)
                    if community:
                        communities.add(community)
                
                # 也可以从标题中提取
                title_elem = item.find('div', class_='title')
                if title_elem:
                    title_text = self.extract_text(title_elem.find('a'))
                    community = self._extract_community_from_title(title_text)
                    if community:
                        communities.add(community)
                        
            except Exception as e:
                logger.debug(f"提取小区信息失败: {e}")
                continue
        
        return communities
    
    def _extract_community_name(self, position_text: str) -> str:
        """从位置信息中提取小区名称"""
        if not position_text:
            return ""
        
        # 位置信息通常格式为：小区名 区域名
        parts = position_text.strip().split()
        if len(parts) >= 1:
            community = parts[0]
            # 过滤掉明显不是小区名的内容
            if self._is_valid_community_name(community):
                return community
        
        return ""
    
    def _extract_community_from_title(self, title_text: str) -> str:
        """从标题中提取小区名称"""
        if not title_text:
            return ""
        
        # 标题通常包含小区名，尝试提取
        # 常见格式：小区名 房型 面积 等信息
        parts = title_text.split()
        for part in parts:
            if self._is_valid_community_name(part):
                return part
        
        return ""
    
    def _is_valid_community_name(self, name: str) -> bool:
        """判断是否是有效的小区名称"""
        if not name or len(name) < 2:
            return False
        
        # 过滤掉明显不是小区名的内容
        invalid_keywords = [
            '室', '厅', '卫', '平', '万', '元', '月', '年',
            '精装', '毛坯', '南北', '朝南', '朝北', '楼层',
            '电梯', '步梯', '新房', '二手', '出租', '出售',
            '急售', '急租', '随时', '看房', '地铁', '公交',
            '学校', '医院', '商场', '超市'
        ]
        
        for keyword in invalid_keywords:
            if keyword in name:
                return False
        
        # 小区名通常包含这些特征
        valid_suffixes = ['小区', '花园', '公寓', '大厦', '广场', '城', '苑', '园', '庭', '居', '府', '墅']
        
        for suffix in valid_suffixes:
            if name.endswith(suffix):
                return True
        
        # 如果没有明显的后缀，但长度合适且不包含无效关键词，也认为可能是小区名
        return len(name) >= 3 and len(name) <= 20
    
    def _has_next_page(self, soup) -> bool:
        """检查是否有下一页"""
        page_box = soup.find('div', class_='page-box')
        if not page_box:
            return False
        
        next_link = page_box.find('a', {'data-role': 'next'})
        return next_link is not None
    
    def geocode_communities(self, communities: Set[str]) -> pd.DataFrame:
        """对小区进行地理编码"""
        logger.info(f"开始对 {len(communities)} 个小区进行地理编码...")
        
        # 准备地址列表
        addresses = []
        community_list = list(communities)
        
        for community in community_list:
            # 构建完整地址
            address = f"上海市浦东新区{community}"
            addresses.append(address)
        
        # 批量地理编码
        coords_list = batch_geocode_addresses(addresses, city="上海", delay=0.8)
        
        # 构建DataFrame
        data = []
        for i, community in enumerate(community_list):
            coords = coords_list[i]
            
            record = {
                'community_name': community,
                'address': addresses[i],
                'longitude': coords[0] if coords else None,
                'latitude': coords[1] if coords else None,
                'geocoded': coords is not None
            }
            data.append(record)
        
        df = pd.DataFrame(data)
        
        # 统计地理编码成功率
        success_count = df['geocoded'].sum()
        success_rate = success_count / len(df) * 100
        
        logger.info(f"地理编码完成，成功率: {success_count}/{len(df)} ({success_rate:.1f}%)")
        
        return df

    def _get_demo_communities(self) -> Set[str]:
        """获取演示用的小区数据"""
        logger.info("使用陆家嘴地区的真实小区名称作为演示数据")

        # 陆家嘴地区的真实小区名称
        demo_communities = {
            "陆家嘴花园",
            "世纪大道小区",
            "东方明珠小区",
            "滨江大厦",
            "世纪公园",
            "金茂大厦",
            "环球金融中心",
            "上海中心大厦",
            "国金中心",
            "正大广场",
            "八佰伴",
            "第一八佰伴",
            "浦东嘉里城",
            "陆家嘴软件园",
            "世纪汇广场",
            "上海国际金融中心",
            "花旗集团大厦",
            "汇丰银行大楼",
            "招商银行大厦",
            "中国银行大厦"
        }

        logger.info(f"生成演示小区数据: {len(demo_communities)} 个小区")
        return demo_communities

    def collect_community_data(self) -> str:
        """收集完整的小区数据"""
        logger.info("开始收集小区数据...")

        # 1. 从房产列表收集小区名称
        communities = self.collect_communities_from_listings()

        if not communities:
            logger.warning("未从链家收集到小区信息，使用模拟数据进行演示")
            communities = self._get_demo_communities()

        if not communities:
            logger.error("未收集到任何小区信息")
            return ""
        
        # 2. 对小区进行地理编码
        community_df = self.geocode_communities(communities)
        
        # 3. 过滤有效的小区数据
        valid_communities = community_df[community_df['geocoded'] == True].copy()
        
        if len(valid_communities) == 0:
            logger.error("没有成功地理编码的小区")
            return ""
        
        # 4. 添加额外信息
        valid_communities = self._add_community_metadata(valid_communities)
        
        # 5. 保存小区数据
        output_file = config.get("PATHS", "raw_housing_data", 
                                "data/raw/communities_lujiazui.csv")
        output_path = get_data_path(output_file)
        
        save_dataframe(valid_communities, output_path)
        
        logger.info(f"小区数据收集完成: {len(valid_communities)} 个有效小区")
        logger.info(f"数据已保存到: {output_path}")
        
        return output_path
    
    def _add_community_metadata(self, df: pd.DataFrame) -> pd.DataFrame:
        """添加小区元数据"""
        # 计算到CBD的直线距离
        cbd_lat = config.getfloat("CBD_INFO", "latitude", 31.239638)
        cbd_lng = config.getfloat("CBD_INFO", "longitude", 121.499767)
        
        distances = []
        for _, row in df.iterrows():
            if pd.notna(row['longitude']) and pd.notna(row['latitude']):
                distance = self._haversine_distance(
                    row['latitude'], row['longitude'], cbd_lat, cbd_lng
                ) / 1000  # 转换为公里
                distances.append(distance)
            else:
                distances.append(None)
        
        df['distance_to_cbd_km'] = distances
        
        # 添加距离等级
        df['distance_level'] = pd.cut(
            df['distance_to_cbd_km'],
            bins=[0, 3, 6, 10, 15, float('inf')],
            labels=['很近', '较近', '适中', '较远', '很远']
        )
        
        # 添加收集时间
        import datetime
        df['collected_at'] = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        return df
    
    def _haversine_distance(self, lat1: float, lon1: float, lat2: float, lon2: float) -> float:
        """计算两点间距离（米）"""
        from math import radians, cos, sin, asin, sqrt
        
        lat1, lon1, lat2, lon2 = map(radians, [lat1, lon1, lat2, lon2])
        dlat = lat2 - lat1
        dlon = lon2 - lon1
        a = sin(dlat/2)**2 + cos(lat1) * cos(lat2) * sin(dlon/2)**2
        c = 2 * asin(sqrt(a))
        r = 6371000  # 地球半径（米）
        return c * r
    
    def get_community_summary(self, community_file: str = None) -> Dict:
        """获取小区数据摘要"""
        if not community_file:
            community_file = config.get("PATHS", "raw_housing_data",
                                      "data/raw/communities_lujiazui.csv")
        
        try:
            from ..utils.file_io import load_dataframe
            df = load_dataframe(get_data_path(community_file))
            
            if df is None:
                return {}
            
            summary = {
                'total_communities': len(df),
                'geocoded_communities': df['geocoded'].sum(),
                'avg_distance_to_cbd': df['distance_to_cbd_km'].mean(),
                'distance_distribution': df['distance_level'].value_counts().to_dict()
            }
            
            return summary
            
        except Exception as e:
            logger.error(f"获取小区摘要失败: {e}")
            return {}
