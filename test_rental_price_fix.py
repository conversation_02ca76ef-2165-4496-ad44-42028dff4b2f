#!/usr/bin/env python3
"""
测试rental_price修复脚本
验证房产数据清洗不再依赖rental_price字段
"""

import logging
import sys
import pandas as pd
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.utils.logger_setup import setup_logger
from src.data_processing.clean_housing_data import HousingDataCleaner

# 设置日志
setup_logger()
logger = logging.getLogger(__name__)

def create_test_housing_data():
    """创建测试房产数据（不包含rental_price）"""
    test_data = {
        'title': [
            '梅园三街坊 2室1厅 精装修',
            '星纪元 1室1厅 豪华装修',
            '陆家嘴花园 3室2厅 简装',
            '世纪公园附近 2室1厅',
            '东方明珠旁 1室1厅'
        ],
        'total_price': [500, 600, 800, 550, 650],  # 万元
        'unit_price': [50000, 60000, 70000, 55000, 65000],  # 元/平米
        'area': [100, 100, 114, 100, 100],  # 平米
        'community': ['梅园三街坊', '星纪元', '陆家嘴花园', '世纪公园', '东方明珠'],
        'house_info': [
            '浦东-陆家嘴-梅园三街坊/100㎡/南/2室1厅1卫/高楼层',
            '浦东-陆家嘴-星纪元/100㎡/东南/1室1厅1卫/中楼层',
            '浦东-陆家嘴-陆家嘴花园/114㎡/西/3室2厅2卫/低楼层',
            '浦东-世纪公园-世纪公园/100㎡/南/2室1厅1卫/高楼层',
            '浦东-陆家嘴-东方明珠/100㎡/东/1室1厅1卫/中楼层'
        ],
        'crawl_time': ['2025-06-04 20:00:00'] * 5,
        'source': ['test_scraper'] * 5
    }
    
    return pd.DataFrame(test_data)

def test_housing_cleaner_without_rental_price():
    """测试房产清洗器在没有rental_price字段时的表现"""
    logger.info("=" * 50)
    logger.info("测试房产清洗器（无rental_price字段）")
    logger.info("=" * 50)
    
    try:
        # 创建测试数据
        test_df = create_test_housing_data()
        logger.info(f"创建测试数据: {len(test_df)} 条记录")
        logger.info(f"字段: {list(test_df.columns)}")
        
        # 确认没有rental_price字段
        if 'rental_price' in test_df.columns:
            logger.error("测试数据包含rental_price字段，测试无效")
            return False
        
        logger.info("✓ 确认测试数据不包含rental_price字段")
        
        # 创建清洗器
        cleaner = HousingDataCleaner()
        
        # 测试各个清洗步骤
        logger.info("测试去重...")
        df = cleaner._remove_duplicates(test_df.copy())
        logger.info(f"✓ 去重完成: {len(df)} 条记录")
        
        logger.info("测试小区信息提取...")
        df = cleaner._extract_community_info(df)
        logger.info(f"✓ 小区信息提取完成: {len(df)} 条记录")
        
        logger.info("测试文本字段清洗...")
        df = cleaner._clean_text_fields(df)
        logger.info(f"✓ 文本字段清洗完成: {len(df)} 条记录")
        
        logger.info("测试数值字段清洗...")
        df = cleaner._clean_numeric_fields(df)
        logger.info(f"✓ 数值字段清洗完成: {len(df)} 条记录")
        
        logger.info("测试面积信息标准化...")
        df = cleaner._standardize_area_info(df)
        logger.info(f"✓ 面积信息标准化完成: {len(df)} 条记录")
        
        logger.info("测试衍生字段添加...")
        df = cleaner._add_derived_fields(df)
        logger.info(f"✓ 衍生字段添加完成: {len(df)} 条记录")
        
        # 添加模拟坐标以测试有效记录过滤
        df['longitude'] = [121.5, 121.51, 121.52, 121.53, 121.54]
        df['latitude'] = [31.24, 31.25, 31.26, 31.27, 31.28]
        
        logger.info("测试有效记录过滤...")
        df = cleaner._filter_valid_records(df)
        logger.info(f"✓ 有效记录过滤完成: {len(df)} 条记录")
        
        # 检查最终结果
        if len(df) > 0:
            logger.info("✓ 房产数据清洗测试成功")
            logger.info(f"最终数据字段: {list(df.columns)}")
            logger.info(f"最终数据行数: {len(df)}")
            return True
        else:
            logger.error("✗ 清洗后数据为空")
            return False
            
    except Exception as e:
        logger.error(f"✗ 房产清洗器测试失败: {e}")
        return False

def test_empty_data_handling():
    """测试空数据处理"""
    logger.info("=" * 50)
    logger.info("测试空数据处理")
    logger.info("=" * 50)
    
    try:
        cleaner = HousingDataCleaner()
        
        # 测试空DataFrame
        empty_df = pd.DataFrame()
        result_df = cleaner._geocode_addresses(empty_df)
        
        if len(result_df) == 0:
            logger.info("✓ 空数据处理正常")
            return True
        else:
            logger.error("✗ 空数据处理异常")
            return False
            
    except Exception as e:
        logger.error(f"✗ 空数据处理测试失败: {e}")
        return False

def test_numeric_field_cleaning():
    """测试数值字段清洗（不包含rental_price）"""
    logger.info("=" * 50)
    logger.info("测试数值字段清洗")
    logger.info("=" * 50)
    
    try:
        cleaner = HousingDataCleaner()
        
        # 创建包含异常值的测试数据
        test_data = pd.DataFrame({
            'total_price': [10, 500, 15000, 600],  # 包含异常值：10万（太低），15000万（太高）
            'unit_price': [1000, 50000, 300000, 60000],  # 包含异常值：1000元/平米（太低），300000元/平米（太高）
            'area_numeric': [5, 100, 1500, 120]  # 包含异常值：5平米（太小），1500平米（太大）
        })
        
        logger.info(f"清洗前数据: {len(test_data)} 条记录")
        logger.info(f"total_price范围: {test_data['total_price'].min()} - {test_data['total_price'].max()}")
        logger.info(f"unit_price范围: {test_data['unit_price'].min()} - {test_data['unit_price'].max()}")
        logger.info(f"area_numeric范围: {test_data['area_numeric'].min()} - {test_data['area_numeric'].max()}")
        
        # 执行数值字段清洗
        cleaned_data = cleaner._clean_numeric_fields(test_data)
        
        logger.info(f"清洗后数据: {len(cleaned_data)} 条记录")
        logger.info(f"total_price有效值: {cleaned_data['total_price'].notna().sum()}")
        logger.info(f"unit_price有效值: {cleaned_data['unit_price'].notna().sum()}")
        logger.info(f"area_numeric有效值: {cleaned_data['area_numeric'].notna().sum()}")
        
        # 检查异常值是否被正确处理
        valid_total_price = cleaned_data['total_price'].notna().sum()
        valid_unit_price = cleaned_data['unit_price'].notna().sum()
        valid_area = cleaned_data['area_numeric'].notna().sum()
        
        if valid_total_price == 2 and valid_unit_price == 2 and valid_area == 2:
            logger.info("✓ 数值字段清洗正确，异常值已被过滤")
            return True
        else:
            logger.error(f"✗ 数值字段清洗异常: total_price={valid_total_price}, unit_price={valid_unit_price}, area={valid_area}")
            return False
            
    except Exception as e:
        logger.error(f"✗ 数值字段清洗测试失败: {e}")
        return False

def main():
    """主函数"""
    logger.info("开始测试rental_price修复效果...")
    
    results = {
        "房产清洗器（无rental_price）": test_housing_cleaner_without_rental_price(),
        "空数据处理": test_empty_data_handling(),
        "数值字段清洗": test_numeric_field_cleaning()
    }
    
    logger.info("=" * 50)
    logger.info("测试结果汇总")
    logger.info("=" * 50)
    
    all_passed = True
    for test_name, result in results.items():
        status = "✓ 通过" if result else "✗ 失败"
        logger.info(f"{test_name}: {status}")
        if not result:
            all_passed = False
    
    if all_passed:
        logger.info("🎉 所有测试通过！rental_price修复成功！")
    else:
        logger.warning("⚠️  部分测试失败，请检查相关配置")
    
    logger.info("=" * 50)
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
