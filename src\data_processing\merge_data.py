"""
数据合并模块
将房产、POI和通勤数据合并为最终数据集
"""

import pandas as pd
import numpy as np
import logging
from typing import List, Dict
from ..utils.config_loader import config
from ..utils.file_io import load_dataframe, save_dataframe, load_dataframe_hybrid, save_dataframe_hybrid, get_data_path

logger = logging.getLogger(__name__)

class DataMerger:
    """数据合并器"""
    
    def __init__(self):
        pass
    
    def merge_all_data(self, housing_file: str = None, poi_file: str = None, 
                      commute_file: str = None, output_file: str = None) -> str:
        """
        合并所有数据
        
        Args:
            housing_file: 房产数据文件路径
            poi_file: POI数据文件路径
            commute_file: 通勤数据文件路径
            output_file: 输出文件路径
            
        Returns:
            输出文件路径
        """
        # 设置默认文件路径
        if not housing_file:
            housing_file = config.get("PATHS", "processed_housing_data",
                                    "data/processed/housing_cleaned_geocoded.csv")
        
        if not poi_file:
            poi_file = config.get("PATHS", "processed_poi_data",
                                "data/processed/poi_gaode_aggregated.csv")
        
        if not commute_file:
            commute_file = config.get("PATHS", "processed_commute_data",
                                    "data/processed/commute_gaode_cleaned.csv")
        
        if not output_file:
            output_file = config.get("PATHS", "final_dataset",
                                   "data/final/final_merged_dataset.csv")
        
        # 加载各数据集：优先从数据库加载
        logger.info("开始加载数据集...")

        housing_df = load_dataframe_hybrid(
            filepath=get_data_path(housing_file),
            table_name='housing_data_cleaned',
            prefer_db=True
        )
        if housing_df is None:
            logger.error(f"无法加载房产数据: {housing_file}")
            return ""

        poi_df = load_dataframe_hybrid(
            filepath=get_data_path(poi_file),
            table_name='poi_aggregated',
            prefer_db=True
        )
        if poi_df is None:
            logger.error(f"无法加载POI数据: {poi_file}")
            return ""

        commute_df = load_dataframe_hybrid(
            filepath=get_data_path(commute_file),
            table_name='commute_data',
            prefer_db=True
        )
        if commute_df is None:
            logger.error(f"无法加载通勤数据: {commute_file}")
            return ""
        
        logger.info(f"数据加载完成:")
        logger.info(f"  房产数据: {len(housing_df)} 条")
        logger.info(f"  POI数据: {len(poi_df)} 条")
        logger.info(f"  通勤数据: {len(commute_df)} 条")
        
        # 数据合并
        merged_df = self._merge_datasets(housing_df, poi_df, commute_df)
        
        # 数据质量检查和清理
        merged_df = self._quality_check_and_clean(merged_df)
        
        # 保存合并后的数据：同时保存到文件和数据库
        output_path = get_data_path(output_file)
        save_dataframe_hybrid(
            df=merged_df,
            filepath=output_path,
            table_name='final_merged_dataset',
            if_exists='replace'
        )
        
        logger.info(f"数据合并完成，最终数据集: {len(merged_df)} 条")
        logger.info(f"数据已保存到: {output_path}")
        
        # 输出数据摘要
        self._print_merge_summary(merged_df)
        
        return output_path
    
    def _merge_datasets(self, housing_df: pd.DataFrame, poi_df: pd.DataFrame, 
                       commute_df: pd.DataFrame) -> pd.DataFrame:
        """合并数据集"""
        logger.info("开始合并数据集...")
        
        # 确保所有数据集都有housing_id列
        if 'housing_id' not in housing_df.columns:
            housing_df = housing_df.reset_index()
            housing_df.rename(columns={'index': 'housing_id'}, inplace=True)
        
        # 以房产数据为基础进行合并
        merged_df = housing_df.copy()
        
        # 合并POI数据
        if 'housing_id' in poi_df.columns:
            merged_df = merged_df.merge(
                poi_df, 
                on='housing_id', 
                how='left',
                suffixes=('', '_poi')
            )
            logger.info("POI数据合并完成")
        else:
            logger.warning("POI数据缺少housing_id列，跳过POI数据合并")
        
        # 合并通勤数据
        if 'housing_id' in commute_df.columns:
            merged_df = merged_df.merge(
                commute_df,
                on='housing_id',
                how='left',
                suffixes=('', '_commute')
            )
            logger.info("通勤数据合并完成")
        else:
            logger.warning("通勤数据缺少housing_id列，跳过通勤数据合并")
        
        # 处理重复列
        merged_df = self._handle_duplicate_columns(merged_df)
        
        return merged_df
    
    def _handle_duplicate_columns(self, df: pd.DataFrame) -> pd.DataFrame:
        """处理重复列"""
        # 找出重复的列
        duplicate_suffixes = ['_poi', '_commute']
        columns_to_drop = []
        
        for col in df.columns:
            for suffix in duplicate_suffixes:
                if col.endswith(suffix):
                    base_col = col.replace(suffix, '')
                    if base_col in df.columns:
                        # 比较两列的数据，保留更完整的一列
                        base_missing = df[base_col].isna().sum()
                        duplicate_missing = df[col].isna().sum()
                        
                        if duplicate_missing < base_missing:
                            # 重复列更完整，删除原列
                            columns_to_drop.append(base_col)
                            df.rename(columns={col: base_col}, inplace=True)
                        else:
                            # 原列更完整，删除重复列
                            columns_to_drop.append(col)
        
        if columns_to_drop:
            df = df.drop(columns=columns_to_drop)
            logger.info(f"删除重复列: {len(columns_to_drop)} 个")
        
        return df
    
    def _quality_check_and_clean(self, df: pd.DataFrame) -> pd.DataFrame:
        """数据质量检查和清理"""
        logger.info("进行数据质量检查...")
        
        original_count = len(df)
        
        # 检查必要字段
        required_fields = ['title', 'longitude', 'latitude']
        missing_required = df[required_fields].isna().any(axis=1)
        
        if missing_required.sum() > 0:
            logger.warning(f"发现 {missing_required.sum()} 条记录缺少必要字段")
            df = df[~missing_required]
        
        # 检查价格字段
        has_price = (df['total_price'].notna()) | (df['unit_price'].notna())
        if (~has_price).sum() > 0:
            logger.warning(f"发现 {(~has_price).sum()} 条记录缺少价格信息")
            df = df[has_price]
        
        # 检查坐标合理性
        valid_coords = (
            (df['longitude'] >= 120.0) & (df['longitude'] <= 122.0) &
            (df['latitude'] >= 30.0) & (df['latitude'] <= 32.0)
        )
        
        if (~valid_coords).sum() > 0:
            logger.warning(f"发现 {(~valid_coords).sum()} 条记录坐标异常")
            df = df[valid_coords]
        
        # 重置索引
        df = df.reset_index(drop=True)
        
        cleaned_count = original_count - len(df)
        if cleaned_count > 0:
            logger.info(f"数据清理完成，删除 {cleaned_count} 条异常记录")
        
        return df
    
    def _print_merge_summary(self, df: pd.DataFrame):
        """打印合并摘要"""
        logger.info("数据合并摘要:")
        logger.info(f"  最终记录数: {len(df)}")
        logger.info(f"  总列数: {len(df.columns)}")
        
        # 数据完整性统计
        logger.info("  数据完整性:")
        
        # 房产基础信息
        basic_fields = ['title', 'total_price', 'rental_price', 'area_numeric']
        for field in basic_fields:
            if field in df.columns:
                completeness = (1 - df[field].isna().sum() / len(df)) * 100
                logger.info(f"    {field}: {completeness:.1f}%")
        
        # POI信息
        poi_categories = config.get_section("GAODE_POI_TYPES").keys()
        poi_complete = 0
        for category in poi_categories:
            count_col = f'{category}_count'
            if count_col in df.columns:
                poi_complete += (df[count_col].notna().sum() / len(df))
        
        if poi_complete > 0:
            poi_completeness = (poi_complete / len(poi_categories)) * 100
            logger.info(f"    POI数据: {poi_completeness:.1f}%")
        
        # 通勤信息
        if 'commute_duration_min' in df.columns:
            commute_completeness = (1 - df['commute_duration_min'].isna().sum() / len(df)) * 100
            logger.info(f"    通勤数据: {commute_completeness:.1f}%")
        
        # 价格分布
        if 'total_price' in df.columns:
            sale_data = df[df['total_price'].notna()]
            if len(sale_data) > 0:
                logger.info(f"  二手房价格分布:")
                logger.info(f"    平均价格: {sale_data['total_price'].mean():.1f} 万元")
                logger.info(f"    价格范围: {sale_data['total_price'].min():.1f} - {sale_data['total_price'].max():.1f} 万元")
        
        if 'rental_price' in df.columns:
            rental_data = df[df['rental_price'].notna()]
            if len(rental_data) > 0:
                logger.info(f"  租房价格分布:")
                logger.info(f"    平均租金: {rental_data['rental_price'].mean():.0f} 元/月")
                logger.info(f"    租金范围: {rental_data['rental_price'].min():.0f} - {rental_data['rental_price'].max():.0f} 元/月")
        
        # 通勤时间分布
        if 'commute_duration_min' in df.columns:
            commute_data = df[df['commute_duration_min'].notna()]
            if len(commute_data) > 0:
                logger.info(f"  通勤时间分布:")
                logger.info(f"    平均通勤时间: {commute_data['commute_duration_min'].mean():.1f} 分钟")
                logger.info(f"    通勤时间范围: {commute_data['commute_duration_min'].min():.1f} - {commute_data['commute_duration_min'].max():.1f} 分钟")
    
    def get_data_summary(self, merged_file: str = None) -> Dict:
        """
        获取合并数据的详细摘要
        
        Args:
            merged_file: 合并数据文件路径
            
        Returns:
            数据摘要字典
        """
        if not merged_file:
            merged_file = config.get("PATHS", "final_dataset",
                                   "data/final/final_merged_dataset.csv")
        
        df = load_dataframe(get_data_path(merged_file))
        if df is None:
            return {}
        
        summary = {
            'total_records': len(df),
            'total_columns': len(df.columns),
            'data_types': df.dtypes.value_counts().to_dict(),
            'missing_data': df.isna().sum().to_dict(),
            'numeric_summary': df.describe().to_dict(),
        }
        
        # 添加分类统计
        categorical_columns = df.select_dtypes(include=['object', 'category']).columns
        summary['categorical_summary'] = {}
        
        for col in categorical_columns:
            if df[col].nunique() < 20:  # 只统计类别数较少的列
                summary['categorical_summary'][col] = df[col].value_counts().to_dict()
        
        return summary
