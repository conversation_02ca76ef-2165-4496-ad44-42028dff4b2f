#!/usr/bin/env python3
"""
测试小区数据过滤功能
验证数据量小于5的小区是否被正确过滤
"""

import logging
import sys
import pandas as pd
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.utils.logger_setup import setup_logger
from src.analysis.community_price_analyzer import CommunityPriceAnalyzer

# 设置日志
setup_logger()
logger = logging.getLogger(__name__)

def create_test_data_with_small_communities():
    """创建包含小数据量小区的测试数据"""
    test_data = {
        'community': [
            # 大小区（10条数据）
            '大小区A', '大小区A', '大小区A', '大小区A', '大小区A',
            '大小区A', '大小区A', '大小区A', '大小区A', '大小区A',
            
            # 中等小区（5条数据）
            '中等小区B', '中等小区B', '中等小区B', '中等小区B', '中等小区B',
            
            # 小小区1（4条数据，应被过滤）
            '小小区C', '小小区C', '小小区C', '小小区C',
            
            # 小小区2（3条数据，应被过滤）
            '小小区D', '小小区D', '小小区D',
            
            # 小小区3（2条数据，应被过滤）
            '小小区E', '小小区E',
            
            # 小小区4（1条数据，应被过滤）
            '小小区F',
            
            # 另一个大小区（6条数据）
            '大小区G', '大小区G', '大小区G', '大小区G', '大小区G', '大小区G'
        ],
        'total_price': [
            # 大小区A
            500, 520, 480, 510, 490, 530, 470, 540, 460, 550,
            # 中等小区B
            600, 620, 580, 610, 590,
            # 小小区C
            700, 720, 680, 710,
            # 小小区D
            800, 820, 780,
            # 小小区E
            900, 920,
            # 小小区F
            1000,
            # 大小区G
            400, 420, 380, 410, 390, 430
        ],
        'unit_price': [
            # 大小区A
            50000, 52000, 48000, 51000, 49000, 53000, 47000, 54000, 46000, 55000,
            # 中等小区B
            60000, 62000, 58000, 61000, 59000,
            # 小小区C
            70000, 72000, 68000, 71000,
            # 小小区D
            80000, 82000, 78000,
            # 小小区E
            90000, 92000,
            # 小小区F
            100000,
            # 大小区G
            40000, 42000, 38000, 41000, 39000, 43000
        ],
        'area': [100] * 31,  # 所有房源都是100平米
        'crawl_time': ['2025-06-04 20:00:00'] * 31,
        'source': ['test_scraper'] * 31
    }
    
    return pd.DataFrame(test_data)

def test_community_filtering():
    """测试小区过滤功能"""
    logger.info("=" * 50)
    logger.info("测试小区数据过滤功能")
    logger.info("=" * 50)
    
    try:
        # 创建测试数据
        test_df = create_test_data_with_small_communities()
        logger.info(f"创建测试数据: {len(test_df)} 条记录")
        
        # 统计原始小区分布
        community_counts = test_df['community'].value_counts()
        logger.info("原始小区数据分布:")
        for community, count in community_counts.items():
            status = "应保留" if count >= 5 else "应过滤"
            logger.info(f"  {community}: {count} 条数据 ({status})")
        
        # 创建分析器
        analyzer = CommunityPriceAnalyzer()
        
        # 预处理数据
        cleaned_df = analyzer._preprocess_data(test_df)
        logger.info(f"预处理后数据: {len(cleaned_df)} 条记录")
        
        # 计算小区统计（包含过滤）
        community_stats = analyzer._calculate_community_price_stats(cleaned_df)
        
        # 验证结果
        logger.info("=" * 30)
        logger.info("过滤结果验证")
        logger.info("=" * 30)
        
        expected_communities = ['大小区A', '中等小区B', '大小区G']  # 数据量≥5的小区
        actual_communities = set(community_stats['community_name'].tolist())
        
        logger.info(f"期望保留的小区: {expected_communities}")
        logger.info(f"实际保留的小区: {list(actual_communities)}")
        
        # 检查是否正确过滤
        success = True
        
        # 检查应该保留的小区是否都保留了
        for expected in expected_communities:
            if expected not in actual_communities:
                logger.error(f"✗ 小区 {expected} 应该保留但被过滤了")
                success = False
            else:
                logger.info(f"✓ 小区 {expected} 正确保留")
        
        # 检查应该过滤的小区是否都被过滤了
        filtered_communities = ['小小区C', '小小区D', '小小区E', '小小区F']
        for filtered in filtered_communities:
            if filtered in actual_communities:
                logger.error(f"✗ 小区 {filtered} 应该被过滤但被保留了")
                success = False
            else:
                logger.info(f"✓ 小区 {filtered} 正确过滤")
        
        # 检查统计数据
        logger.info("=" * 30)
        logger.info("统计数据验证")
        logger.info("=" * 30)
        
        for _, row in community_stats.iterrows():
            community = row['community_name']
            count = row['housing_count']
            avg_price = row.get('avg_unit_price', 0)
            
            logger.info(f"{community}: {count} 套房源, 平均单价: {avg_price:.0f} 元/平米")
            
            if count < 5:
                logger.error(f"✗ 小区 {community} 数据量 {count} < 5，不应该被保留")
                success = False
        
        if success:
            logger.info("🎉 小区过滤功能测试通过！")
            return True
        else:
            logger.error("❌ 小区过滤功能测试失败")
            return False
            
    except Exception as e:
        logger.error(f"✗ 测试过程中出现异常: {e}")
        return False

def test_edge_cases():
    """测试边界情况"""
    logger.info("=" * 50)
    logger.info("测试边界情况")
    logger.info("=" * 50)
    
    try:
        analyzer = CommunityPriceAnalyzer()
        
        # 测试1: 空数据
        empty_df = pd.DataFrame(columns=['community_clean', 'total_price', 'unit_price', 'area_clean'])
        result = analyzer._calculate_community_price_stats(empty_df)
        if len(result) == 0:
            logger.info("✓ 空数据处理正确")
        else:
            logger.error("✗ 空数据处理异常")
            return False
        
        # 测试2: 所有小区数据都不足5条
        small_data = pd.DataFrame({
            'community_clean': ['A', 'A', 'B', 'B', 'B', 'C'],
            'total_price': [500, 520, 600, 620, 580, 700],
            'unit_price': [50000, 52000, 60000, 62000, 58000, 70000],
            'area_clean': [100, 100, 100, 100, 100, 100]
        })
        
        result = analyzer._calculate_community_price_stats(small_data)
        if len(result) == 0:
            logger.info("✓ 全部小区数据不足时正确返回空结果")
        else:
            logger.error("✗ 全部小区数据不足时处理异常")
            return False
        
        # 测试3: 恰好5条数据的小区
        exact_data = pd.DataFrame({
            'community_clean': ['X'] * 5,
            'total_price': [500, 520, 480, 510, 490],
            'unit_price': [50000, 52000, 48000, 51000, 49000],
            'area_clean': [100] * 5
        })
        
        result = analyzer._calculate_community_price_stats(exact_data)
        if len(result) == 1 and result.iloc[0]['housing_count'] == 5:
            logger.info("✓ 恰好5条数据的小区正确保留")
        else:
            logger.error("✗ 恰好5条数据的小区处理异常")
            return False
        
        logger.info("🎉 边界情况测试通过！")
        return True
        
    except Exception as e:
        logger.error(f"✗ 边界情况测试失败: {e}")
        return False

def main():
    """主函数"""
    logger.info("开始测试小区数据过滤功能...")
    
    results = {
        "小区过滤功能": test_community_filtering(),
        "边界情况": test_edge_cases()
    }
    
    logger.info("=" * 50)
    logger.info("测试结果汇总")
    logger.info("=" * 50)
    
    all_passed = True
    for test_name, result in results.items():
        status = "✓ 通过" if result else "✗ 失败"
        logger.info(f"{test_name}: {status}")
        if not result:
            all_passed = False
    
    if all_passed:
        logger.info("🎉 所有测试通过！小区过滤功能正常！")
    else:
        logger.warning("⚠️  部分测试失败，请检查过滤逻辑")
    
    logger.info("=" * 50)
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
