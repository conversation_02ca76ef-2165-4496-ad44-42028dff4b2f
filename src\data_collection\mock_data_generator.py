"""
模拟数据生成器
用于生成测试数据，替代真实的爬虫数据
"""

import random
import pandas as pd
import logging
from typing import List, Dict
from ..utils.config_loader import config
from ..utils.file_io import save_dataframe, get_data_path

logger = logging.getLogger(__name__)

class MockDataGenerator:
    """模拟数据生成器"""
    
    def __init__(self):
        self.communities = [
            "陆家嘴花园", "世纪大道小区", "东方明珠公寓", "滨江大厦", 
            "金茂大厦", "环球金融中心", "上海中心大厦", "国金中心",
            "正大广场", "八佰伴", "浦东嘉里城", "陆家嘴软件园",
            "世纪汇广场", "花旗集团大厦", "汇丰银行大楼", "招商银行大厦",
            "中国银行大厦", "浦发银行大厦", "交通银行大厦", "建设银行大厦"
        ]
        
        self.districts = ["浦东新区", "黄浦区", "静安区", "徐汇区"]
        self.decorations = ["精装修", "简装修", "毛坯", "豪华装修"]
        self.directions = ["南北", "东西", "朝南", "朝北", "东南", "西南"]
        
        # 陆家嘴地区的坐标范围
        self.lng_range = (121.48, 121.52)
        self.lat_range = (31.22, 31.26)
    
    def generate_housing_data(self, count: int = 100) -> List[Dict]:
        """生成模拟房产数据"""
        logger.info(f"生成 {count} 条模拟房产数据...")
        
        houses = []
        
        for i in range(count):
            community = random.choice(self.communities)
            district = random.choice(self.districts)
            
            # 生成房型
            rooms = random.randint(1, 4)
            halls = random.randint(1, 2)
            bathrooms = random.randint(1, 2)
            room_type = f"{rooms}室{halls}厅{bathrooms}卫"
            
            # 生成面积和价格
            area = random.randint(50, 200)
            unit_price = random.randint(40000, 120000)  # 元/平米
            total_price = area * unit_price / 10000  # 万元
            
            # 生成坐标
            longitude = random.uniform(*self.lng_range)
            latitude = random.uniform(*self.lat_range)
            
            house = {
                'title': f"{community} {room_type} {area}平米",
                'community': community,
                'district': district,
                'rooms': room_type,
                'area': area,
                'total_price': round(total_price, 1),
                'unit_price': unit_price,
                'decoration': random.choice(self.decorations),
                'direction': random.choice(self.directions),
                'longitude': round(longitude, 6),
                'latitude': round(latitude, 6),
                'type': 'sale',
                'house_info': f"{room_type} | {area}平米 | {random.choice(self.directions)} | {random.choice(self.decorations)}",
                'position_info': f"{district} {community}",
                'detail_url': f"https://sh.lianjia.com/ershoufang/{i+1}.html"
            }
            
            houses.append(house)
        
        logger.info(f"模拟房产数据生成完成: {len(houses)} 条")
        return houses
    
    def generate_community_data(self, count: int = 20) -> pd.DataFrame:
        """生成模拟小区数据"""
        logger.info(f"生成 {count} 条模拟小区数据...")
        
        communities = random.sample(self.communities, min(count, len(self.communities)))
        
        data = []
        for community in communities:
            # 生成坐标
            longitude = random.uniform(*self.lng_range)
            latitude = random.uniform(*self.lat_range)
            
            # 计算到CBD的距离
            cbd_lng = config.getfloat("CBD_INFO", "longitude", 121.499767)
            cbd_lat = config.getfloat("CBD_INFO", "latitude", 31.239638)
            
            distance = self._calculate_distance(latitude, longitude, cbd_lat, cbd_lng)
            
            record = {
                'community_name': community,
                'address': f"上海市浦东新区{community}",
                'longitude': round(longitude, 6),
                'latitude': round(latitude, 6),
                'geocoded': True,
                'distance_to_cbd_km': round(distance, 2),
                'distance_level': self._get_distance_level(distance),
                'collected_at': pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            
            data.append(record)
        
        df = pd.DataFrame(data)
        logger.info(f"模拟小区数据生成完成: {len(df)} 条")
        return df
    
    def generate_poi_data(self, coords_str: str, category: str, count: int = 50) -> List[Dict]:
        """生成模拟POI数据"""
        logger.info(f"为坐标 {coords_str} 生成 {count} 条 {category} POI数据...")
        
        # 解析坐标
        try:
            lng, lat = map(float, coords_str.split(','))
        except:
            lng, lat = 121.5, 31.24
        
        # 不同类别的POI名称模板
        poi_templates = {
            'catering': [
                "麦当劳", "肯德基", "星巴克", "必胜客", "海底捞", "西贝莜面村",
                "外婆家", "绿茶餐厅", "新白鹿餐厅", "小南国", "鼎泰丰", "和府捞面"
            ],
            'shopping': [
                "正大广场", "国金中心", "环球港", "来福士", "第一八佰伴", "世纪汇",
                "嘉里城", "太平洋百货", "久光百货", "美罗城", "港汇恒隆", "iapm"
            ],
            'education': [
                "浦东新区第一小学", "建平中学", "华师大二附中", "上海中学东校",
                "进才中学", "洋泾中学", "浦东外国语学校", "平和双语学校"
            ],
            'medical': [
                "仁济医院", "东方医院", "浦东医院", "第六人民医院", "新华医院",
                "儿童医学中心", "胸科医院", "肿瘤医院", "眼耳鼻喉科医院"
            ],
            'transportation': [
                "陆家嘴地铁站", "东昌路地铁站", "世纪大道地铁站", "浦电路地铁站",
                "蓝村路地铁站", "塘桥地铁站", "南京东路地铁站", "人民广场地铁站"
            ]
        }
        
        templates = poi_templates.get(category, ["通用POI"])
        
        pois = []
        for i in range(count):
            # 在坐标附近生成随机位置
            poi_lng = lng + random.uniform(-0.01, 0.01)
            poi_lat = lat + random.uniform(-0.01, 0.01)
            
            # 随机选择POI名称
            base_name = random.choice(templates)
            poi_name = f"{base_name}({random.choice(['陆家嘴店', '浦东店', '世纪大道店', '东方明珠店'])})"
            
            # 生成地址
            address = f"浦东新区世纪大道{random.randint(100, 2000)}号"
            
            poi = {
                'name': poi_name,
                'type': category,
                'address': address,
                'location': f"{poi_lng:.6f},{poi_lat:.6f}",
                'distance': random.randint(50, 1000),  # 距离（米）
                'tel': f"021-{random.randint(50000000, 69999999)}",
                'business_area': '陆家嘴',
                'adcode': '310115',
                'citycode': '021'
            }
            
            pois.append(poi)
        
        logger.info(f"模拟POI数据生成完成: {len(pois)} 条")
        return pois
    
    def generate_commute_data(self, housing_df: pd.DataFrame) -> List[Dict]:
        """生成模拟通勤数据"""
        logger.info(f"为 {len(housing_df)} 条房产生成模拟通勤数据...")
        
        commute_data = []
        
        for idx, row in housing_df.iterrows():
            # 根据距离估算通勤时间
            distance_km = getattr(row, 'distance_to_cbd_km', random.uniform(2, 15))
            
            # 基础通勤时间（分钟）
            base_time = distance_km * 3 + random.uniform(5, 15)
            
            # 换乘次数
            transfers = 0 if distance_km < 5 else random.randint(0, 2)
            
            # 步行距离
            walking_km = random.uniform(0.5, 1.5)
            
            commute_record = {
                'housing_id': idx,
                'title': row.get('title', ''),
                'community': row.get('community', ''),
                'longitude': row.get('longitude', 0),
                'latitude': row.get('latitude', 0),
                'cbd_longitude': config.getfloat("CBD_INFO", "longitude", 121.499767),
                'cbd_latitude': config.getfloat("CBD_INFO", "latitude", 31.239638),
                'duration_min': round(base_time, 1),
                'transfers': transfers,
                'walking_km': round(walking_km, 2),
                'route_found': True
            }
            
            commute_data.append(commute_record)
        
        logger.info(f"模拟通勤数据生成完成: {len(commute_data)} 条")
        return commute_data
    
    def _calculate_distance(self, lat1: float, lon1: float, lat2: float, lon2: float) -> float:
        """计算两点间距离（公里）"""
        from math import radians, cos, sin, asin, sqrt
        
        lat1, lon1, lat2, lon2 = map(radians, [lat1, lon1, lat2, lon2])
        dlat = lat2 - lat1
        dlon = lon2 - lon1
        a = sin(dlat/2)**2 + cos(lat1) * cos(lat2) * sin(dlon/2)**2
        c = 2 * asin(sqrt(a))
        r = 6371  # 地球半径（公里）
        return c * r
    
    def _get_distance_level(self, distance: float) -> str:
        """获取距离等级"""
        if distance < 3:
            return "很近"
        elif distance < 6:
            return "较近"
        elif distance < 10:
            return "适中"
        elif distance < 15:
            return "较远"
        else:
            return "很远"
    
    def save_mock_housing_data(self, count: int = 100) -> str:
        """生成并保存模拟房产数据"""
        houses = self.generate_housing_data(count)

        filename = "data/raw/housing_listings_lujiazui_mock.csv"
        filepath = get_data_path(filename)

        df = pd.DataFrame(houses)
        save_dataframe(df, filepath)

        logger.info(f"模拟房产数据已保存: {filepath}")
        return filepath

    def save_mock_community_data(self, count: int = 20) -> str:
        """生成并保存模拟小区数据"""
        df = self.generate_community_data(count)

        filename = "data/raw/communities_lujiazui_mock.csv"
        filepath = get_data_path(filename)

        save_dataframe(df, filepath)

        logger.info(f"模拟小区数据已保存: {filepath}")
        return filepath
