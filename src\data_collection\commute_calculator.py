"""
通勤时间计算器
使用高德API计算小区到CBD的通勤时间
"""

import pandas as pd
import logging
from typing import Dict, Optional
from tqdm import tqdm
from ..utils.config_loader import config
from ..utils.api_helpers import get_transit_route_gaode
from ..utils.file_io import load_dataframe, save_dataframe, get_data_path

logger = logging.getLogger(__name__)

class CommuteCalculator:
    """通勤时间计算器"""
    
    def __init__(self):
        # CBD坐标
        self.cbd_lat = config.getfloat("CBD_INFO", "latitude", 31.239638)
        self.cbd_lng = config.getfloat("CBD_INFO", "longitude", 121.499767)
        self.cbd_coords = f"{self.cbd_lng},{self.cbd_lat}"
        
        # 路径策略
        self.strategy = config.getint("SCRAPER_PARAMS", "commute_strategy_gaode", 0)
        
        logger.info(f"CBD坐标: {self.cbd_coords}")
        logger.info(f"路径策略: {self.strategy}")
    
    def calculate_commute_times(self, housing_file: str = None) -> str:
        """
        计算所有小区到CBD的通勤时间
        
        Args:
            housing_file: 房产数据文件路径
            
        Returns:
            保存的通勤数据文件路径
        """
        # 加载房产数据
        if not housing_file:
            housing_file = config.get("PATHS", "processed_housing_data",
                                    "data/processed/housing_cleaned_geocoded.csv")
        
        housing_df = load_dataframe(get_data_path(housing_file))
        if housing_df is None:
            logger.error(f"无法加载房产数据: {housing_file}")
            return ""
        
        logger.info(f"加载房产数据: {len(housing_df)} 条记录")
        
        # 计算通勤时间
        commute_data = []
        
        for idx, row in tqdm(housing_df.iterrows(), total=len(housing_df), desc="计算通勤时间"):
            try:
                # 检查经纬度
                if pd.isna(row.get('longitude')) or pd.isna(row.get('latitude')):
                    logger.warning(f"跳过无坐标的房产: {row.get('title', 'Unknown')}")
                    continue
                
                # 构建起点坐标
                origin_coords = f"{row['longitude']},{row['latitude']}"
                
                # 计算通勤路径
                route_info = get_transit_route_gaode(
                    origin_coords, 
                    self.cbd_coords,
                    city_code="021",
                    strategy=self.strategy
                )
                
                # 构建通勤数据记录
                commute_record = {
                    'housing_id': idx,
                    'title': row.get('title', ''),
                    'community': row.get('community', ''),
                    'longitude': row['longitude'],
                    'latitude': row['latitude'],
                    'cbd_longitude': self.cbd_lng,
                    'cbd_latitude': self.cbd_lat,
                }
                
                if route_info:
                    commute_record.update({
                        'commute_duration_min': route_info['duration_min'],
                        'commute_transfers': route_info['transfers'],
                        'commute_walking_km': route_info['walking_km'],
                        'route_found': True
                    })
                    logger.debug(f"通勤计算成功: {commute_record['title']} - {route_info['duration_min']:.1f}分钟")
                else:
                    commute_record.update({
                        'commute_duration_min': None,
                        'commute_transfers': None,
                        'commute_walking_km': None,
                        'route_found': False
                    })
                    logger.warning(f"未找到通勤路径: {row.get('title', 'Unknown')}")
                
                commute_data.append(commute_record)
                
            except Exception as e:
                logger.error(f"计算通勤时间失败 (房产ID: {idx}): {e}")
                continue
        
        # 保存通勤数据
        output_file = config.get("PATHS", "raw_commute_data",
                                "data/raw/commute_gaode_to_cbd.csv")
        output_path = get_data_path(output_file)
        
        if commute_data:
            commute_df = pd.DataFrame(commute_data)
            save_dataframe(commute_df, output_path)
            logger.info(f"通勤数据已保存: {output_path}")
            
            # 输出统计信息
            self._print_commute_stats(commute_df)
        else:
            logger.warning("无通勤数据可保存")
        
        return output_path
    
    def calculate_single_commute(self, longitude: float, latitude: float) -> Optional[Dict]:
        """
        计算单个位置到CBD的通勤时间
        
        Args:
            longitude: 经度
            latitude: 纬度
            
        Returns:
            通勤信息字典或None
        """
        origin_coords = f"{longitude},{latitude}"
        
        route_info = get_transit_route_gaode(
            origin_coords,
            self.cbd_coords,
            city_code="021",
            strategy=self.strategy
        )
        
        if route_info:
            return {
                'duration_min': route_info['duration_min'],
                'transfers': route_info['transfers'],
                'walking_km': route_info['walking_km'],
                'origin_coords': origin_coords,
                'destination_coords': self.cbd_coords
            }
        
        return None
    
    def _print_commute_stats(self, commute_df: pd.DataFrame):
        """打印通勤统计信息"""
        total_count = len(commute_df)
        success_count = commute_df['route_found'].sum()
        
        logger.info(f"通勤计算统计:")
        logger.info(f"  总计: {total_count} 条记录")
        logger.info(f"  成功: {success_count} 条 ({success_count/total_count*100:.1f}%)")
        
        if success_count > 0:
            valid_commutes = commute_df[commute_df['route_found'] == True]
            
            avg_duration = valid_commutes['commute_duration_min'].mean()
            min_duration = valid_commutes['commute_duration_min'].min()
            max_duration = valid_commutes['commute_duration_min'].max()
            
            avg_transfers = valid_commutes['commute_transfers'].mean()
            avg_walking = valid_commutes['commute_walking_km'].mean()
            
            logger.info(f"  平均通勤时间: {avg_duration:.1f} 分钟")
            logger.info(f"  最短通勤时间: {min_duration:.1f} 分钟")
            logger.info(f"  最长通勤时间: {max_duration:.1f} 分钟")
            logger.info(f"  平均换乘次数: {avg_transfers:.1f} 次")
            logger.info(f"  平均步行距离: {avg_walking:.2f} 公里")
    
    def get_commute_summary(self, commute_file: str = None) -> Dict:
        """
        获取通勤数据摘要
        
        Args:
            commute_file: 通勤数据文件路径
            
        Returns:
            数据摘要
        """
        if not commute_file:
            commute_file = config.get("PATHS", "raw_commute_data",
                                    "data/raw/commute_gaode_to_cbd.csv")
        
        commute_df = load_dataframe(get_data_path(commute_file))
        if commute_df is None:
            return {}
        
        total_count = len(commute_df)
        success_count = commute_df['route_found'].sum() if 'route_found' in commute_df.columns else 0
        
        summary = {
            'total_records': total_count,
            'successful_routes': success_count,
            'success_rate': success_count / total_count if total_count > 0 else 0,
        }
        
        if success_count > 0:
            valid_commutes = commute_df[commute_df['route_found'] == True]
            
            summary.update({
                'avg_duration_min': valid_commutes['commute_duration_min'].mean(),
                'min_duration_min': valid_commutes['commute_duration_min'].min(),
                'max_duration_min': valid_commutes['commute_duration_min'].max(),
                'avg_transfers': valid_commutes['commute_transfers'].mean(),
                'avg_walking_km': valid_commutes['commute_walking_km'].mean(),
            })
            
            # 通勤时间分布
            duration_ranges = [
                (0, 30, 'under_30min'),
                (30, 60, '30_60min'),
                (60, 90, '60_90min'),
                (90, float('inf'), 'over_90min')
            ]
            
            for min_val, max_val, label in duration_ranges:
                count = len(valid_commutes[
                    (valid_commutes['commute_duration_min'] >= min_val) & 
                    (valid_commutes['commute_duration_min'] < max_val)
                ])
                summary[f'count_{label}'] = count
                summary[f'pct_{label}'] = count / success_count if success_count > 0 else 0
        
        return summary
