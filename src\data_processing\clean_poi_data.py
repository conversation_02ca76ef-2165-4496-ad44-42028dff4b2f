"""
POI数据清洗模块
处理和聚合高德POI数据
"""

import pandas as pd
import numpy as np
import logging
from typing import Dict, List, Any
from ..utils.config_loader import config
from ..utils.file_io import load_json, save_dataframe, get_data_path

logger = logging.getLogger(__name__)

class POIDataCleaner:
    """POI数据清洗器"""
    
    def __init__(self):
        self.poi_categories = self._load_poi_categories()
        
    def _load_poi_categories(self) -> Dict[str, str]:
        """加载POI类别配置"""
        return config.get_section("GAODE_POI_TYPES")
    
    def clean_poi_data(self, input_file: str = None, output_file: str = None) -> str:
        """
        清洗和聚合POI数据
        
        Args:
            input_file: 输入JSON文件路径
            output_file: 输出CSV文件路径
            
        Returns:
            输出文件路径
        """
        # 设置文件路径
        if not input_file:
            input_file = config.get("PATHS", "raw_poi_data",
                                  "data/raw/poi_gaode_around_housing.json")
        
        if not output_file:
            output_file = config.get("PATHS", "processed_poi_data",
                                   "data/processed/poi_gaode_aggregated.csv")
        
        # 加载POI数据
        poi_data = load_json(get_data_path(input_file))
        if not poi_data:
            logger.error(f"无法加载POI数据: {input_file}")
            return ""
        
        logger.info(f"开始处理POI数据，共 {len(poi_data)} 个小区")
        
        # 处理每个小区的POI数据
        processed_data = []
        
        for housing_poi in poi_data:
            try:
                processed_record = self._process_housing_poi(housing_poi)
                if processed_record:
                    processed_data.append(processed_record)
            except Exception as e:
                logger.error(f"处理POI数据失败 (小区: {housing_poi.get('title', 'Unknown')}): {e}")
                continue
        
        # 转换为DataFrame并保存
        if processed_data:
            df = pd.DataFrame(processed_data)
            output_path = get_data_path(output_file)
            save_dataframe(df, output_path)
            
            logger.info(f"POI数据处理完成，共 {len(df)} 条记录")
            logger.info(f"数据已保存到: {output_path}")
            
            # 输出统计信息
            self._print_poi_stats(df)
        else:
            logger.warning("无POI数据可保存")
            return ""
        
        return output_path
    
    def _process_housing_poi(self, housing_poi: Dict) -> Dict:
        """处理单个小区的POI数据"""
        result = {
            'housing_id': housing_poi.get('housing_id'),
            'title': housing_poi.get('title', ''),
            'community': housing_poi.get('community', ''),
            'longitude': housing_poi.get('longitude'),
            'latitude': housing_poi.get('latitude'),
        }
        
        poi_data = housing_poi.get('poi_data', {})
        
        # 处理每个POI类别
        for category in self.poi_categories.keys():
            pois = poi_data.get(category, [])
            
            # 基础统计
            result[f'{category}_count'] = len(pois)
            
            if pois:
                # 计算平均评分（如果有）
                ratings = self._extract_ratings(pois)
                if ratings:
                    result[f'{category}_avg_rating'] = np.mean(ratings)
                    result[f'{category}_max_rating'] = np.max(ratings)
                else:
                    result[f'{category}_avg_rating'] = np.nan
                    result[f'{category}_max_rating'] = np.nan
                
                # 计算距离统计
                distances = self._calculate_distances(pois, housing_poi)
                if distances:
                    result[f'{category}_avg_distance'] = np.mean(distances)
                    result[f'{category}_min_distance'] = np.min(distances)
                else:
                    result[f'{category}_avg_distance'] = np.nan
                    result[f'{category}_min_distance'] = np.nan
                
                # 特殊处理某些类别
                if category == 'transportation':
                    result.update(self._process_transportation_poi(pois))
                elif category == 'education':
                    result.update(self._process_education_poi(pois))
                elif category == 'medical':
                    result.update(self._process_medical_poi(pois))
            else:
                # 无POI时的默认值
                result[f'{category}_avg_rating'] = np.nan
                result[f'{category}_max_rating'] = np.nan
                result[f'{category}_avg_distance'] = np.nan
                result[f'{category}_min_distance'] = np.nan
        
        return result
    
    def _extract_ratings(self, pois: List[Dict]) -> List[float]:
        """提取POI评分"""
        ratings = []
        
        for poi in pois:
            # 尝试从不同字段提取评分
            rating = None
            
            # 检查biz_ext字段
            biz_ext = poi.get('biz_ext', {})
            if isinstance(biz_ext, dict):
                rating = biz_ext.get('rating') or biz_ext.get('score')
            
            # 检查其他可能的评分字段
            if rating is None:
                rating = poi.get('rating') or poi.get('score')
            
            if rating is not None:
                try:
                    rating_float = float(rating)
                    if 0 <= rating_float <= 5:  # 假设评分范围是0-5
                        ratings.append(rating_float)
                except (ValueError, TypeError):
                    continue
        
        return ratings
    
    def _calculate_distances(self, pois: List[Dict], housing_poi: Dict) -> List[float]:
        """计算POI到小区的距离"""
        distances = []
        housing_lng = housing_poi.get('longitude')
        housing_lat = housing_poi.get('latitude')
        
        if housing_lng is None or housing_lat is None:
            return distances
        
        for poi in pois:
            location = poi.get('location', '')
            if location:
                try:
                    # 解析POI坐标 "lng,lat"
                    poi_lng, poi_lat = map(float, location.split(','))
                    
                    # 计算简单的欧几里得距离（米）
                    # 注意：这是近似计算，实际应用中可能需要更精确的地理距离计算
                    distance = self._haversine_distance(
                        housing_lat, housing_lng, poi_lat, poi_lng
                    )
                    distances.append(distance)
                except (ValueError, IndexError):
                    continue
        
        return distances
    
    def _haversine_distance(self, lat1: float, lon1: float, lat2: float, lon2: float) -> float:
        """
        使用Haversine公式计算两点间距离（米）
        """
        from math import radians, cos, sin, asin, sqrt
        
        # 转换为弧度
        lat1, lon1, lat2, lon2 = map(radians, [lat1, lon1, lat2, lon2])
        
        # Haversine公式
        dlat = lat2 - lat1
        dlon = lon2 - lon1
        a = sin(dlat/2)**2 + cos(lat1) * cos(lat2) * sin(dlon/2)**2
        c = 2 * asin(sqrt(a))
        
        # 地球半径（米）
        r = 6371000
        
        return c * r
    
    def _process_transportation_poi(self, pois: List[Dict]) -> Dict:
        """特殊处理交通POI"""
        result = {}
        
        subway_count = 0
        bus_count = 0
        
        for poi in pois:
            type_code = poi.get('typecode', '')
            if type_code.startswith('150500'):  # 地铁站
                subway_count += 1
            elif type_code.startswith('150700'):  # 公交站
                bus_count += 1
        
        result['subway_station_count'] = subway_count
        result['bus_station_count'] = bus_count
        
        return result
    
    def _process_education_poi(self, pois: List[Dict]) -> Dict:
        """特殊处理教育POI"""
        result = {}
        
        school_types = {
            'kindergarten': 0,  # 幼儿园
            'primary_school': 0,  # 小学
            'middle_school': 0,  # 中学
            'university': 0,  # 大学
        }
        
        for poi in pois:
            name = poi.get('name', '').lower()
            type_code = poi.get('typecode', '')
            
            if '幼儿园' in poi.get('name', '') or '141200' in type_code:
                school_types['kindergarten'] += 1
            elif '小学' in poi.get('name', '') or '141100' in type_code:
                school_types['primary_school'] += 1
            elif any(x in poi.get('name', '') for x in ['中学', '高中']) or '141000' in type_code:
                school_types['middle_school'] += 1
            elif '大学' in poi.get('name', '') or '141300' in type_code:
                school_types['university'] += 1
        
        for school_type, count in school_types.items():
            result[f'{school_type}_count'] = count
        
        return result
    
    def _process_medical_poi(self, pois: List[Dict]) -> Dict:
        """特殊处理医疗POI"""
        result = {}
        
        hospital_count = 0
        clinic_count = 0
        pharmacy_count = 0
        
        for poi in pois:
            name = poi.get('name', '')
            type_code = poi.get('typecode', '')
            
            if '医院' in name or '090100' in type_code:
                hospital_count += 1
            elif any(x in name for x in ['诊所', '卫生站', '社区医疗']) or '090200' in type_code:
                clinic_count += 1
            elif '药店' in name or '090300' in type_code:
                pharmacy_count += 1
        
        result['hospital_count'] = hospital_count
        result['clinic_count'] = clinic_count
        result['pharmacy_count'] = pharmacy_count
        
        return result
    
    def _print_poi_stats(self, df: pd.DataFrame):
        """打印POI统计信息"""
        logger.info("POI数据统计:")
        
        for category in self.poi_categories.keys():
            count_col = f'{category}_count'
            if count_col in df.columns:
                total_pois = df[count_col].sum()
                avg_per_housing = df[count_col].mean()
                max_per_housing = df[count_col].max()
                
                logger.info(f"  {category}:")
                logger.info(f"    总数: {total_pois}")
                logger.info(f"    平均每小区: {avg_per_housing:.1f}")
                logger.info(f"    单小区最多: {max_per_housing}")
        
        # 整体统计
        total_columns = [f'{cat}_count' for cat in self.poi_categories.keys()]
        existing_columns = [col for col in total_columns if col in df.columns]
        
        if existing_columns:
            df['total_poi_count'] = df[existing_columns].sum(axis=1)
            logger.info(f"  总体POI统计:")
            logger.info(f"    平均每小区POI总数: {df['total_poi_count'].mean():.1f}")
            logger.info(f"    POI最多的小区: {df['total_poi_count'].max()}")
            logger.info(f"    POI最少的小区: {df['total_poi_count'].min()}")
