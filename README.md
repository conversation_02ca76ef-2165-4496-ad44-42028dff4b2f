# 上海陆家嘴CBD周边住宅租售价格与多维度关联度研究

## 项目概述

本项目旨在探究上海陆家嘴CBD周边住宅租售价格与通勤便利性、生活配套设施之间的量化关联。通过集成高德地图API，获取准确的地理空间数据，进行深度数据分析。

## 数据来源

- **房产数据**：链家上海二手房/租房数据
- **通勤数据**：高德路径规划API
- **POI数据**：高德POI周边搜索API
- **地理编码**：高德地理编码API

## 技术栈

- Python 3.8+
- 数据处理：pandas, numpy
- 网络爬虫：selenium, webdriver-manager
- 网络请求：requests, beautifulsoup4
- 数据可视化：matplotlib, seaborn, folium
- 统计分析：scipy, scikit-learn

## 快速开始

1. 克隆项目并安装依赖：
```bash
pip install -r requirements.txt
```

2. 配置API密钥：
   - 复制 `config/config.ini.template` 为 `config/config.ini`
   - 填入您的高德地图API密钥

3. 运行数据收集和分析：
```bash
# 主程序（基于房源的分析）
python src/main.py

# 或使用统一启动器
python scripts/run_analysis.py main
```

## 🆕 Selenium爬虫

项目已升级使用Selenium WebDriver进行数据爬取，支持：

- **自动化浏览器操作**：模拟真实用户行为
- **手动登录支持**：遇到登录页面时可手动完成登录
- **反检测机制**：随机User-Agent、延时等
- **目标网站**：https://sh.lianjia.com/ershoufang/lujiazui/

### 快速体验

```bash
# 演示爬虫功能（只爬取1页）
python demo_selenium_scraper.py

# 独立运行爬虫
python run_selenium_scraper.py

# 测试爬虫配置
python test_selenium_scraper.py
```

详细使用说明请参考：[Selenium爬虫指南](SELENIUM_SCRAPER_GUIDE.md)

## 项目结构

```
cbd_housing_analysis/
├── config/                 # 配置文件
├── src/                    # 源代码
│   ├── data_collection/    # 数据采集
│   ├── data_processing/    # 数据处理
│   ├── feature_engineering/# 特征工程
│   ├── analysis/          # 统计分析
│   ├── visualization/     # 可视化
│   ├── utils/             # 工具函数
│   └── main.py            # 主程序入口
├── scripts/               # 分析脚本
│   ├── community_analysis/ # 小区分析脚本
│   │   ├── run_community_analysis.py        # 小区综合分析
│   │   └── run_community_housing_analysis.py # 小区房源聚合分析
│   ├── run_analysis.py    # 统一启动器
│   └── README.md          # 脚本说明文档
├── data/                  # 数据文件
├── notebooks/             # Jupyter笔记本
└── outputs/               # 输出结果
```

## 注意事项

- 请确保高德API密钥有效且有足够的调用配额
- 数据采集过程中会有API调用延时，请耐心等待
- 建议在虚拟环境中运行项目

## 项目特色

### 🚀 核心功能
- **智能数据收集**：使用Selenium自动化爬取链家房产数据，集成高德地图API获取精准地理信息
- **多维度分析**：结合房价、通勤便利性、生活配套设施进行综合评估
- **特征工程**：自动生成生活便利度指数、通勤便捷度指数、宜居指数等衍生特征
- **可视化分析**：提供丰富的图表和地图可视化功能

### 🛠️ 技术亮点
- **模块化设计**：清晰的代码结构，易于维护和扩展
- **配置驱动**：通过配置文件灵活调整参数，无需修改代码
- **错误处理**：完善的异常处理和日志记录机制
- **API集成**：高效的高德地图API调用，支持批量处理和限流控制

### 📊 数据维度
- **房产信息**：价格、面积、房型、装修等基础信息
- **位置数据**：精确的经纬度坐标，到CBD距离
- **交通便利性**：公交通勤时间、换乘次数、步行距离
- **生活配套**：周边餐饮、购物、医疗、教育、休闲设施统计

## 🎯 多种分析方式

### 主程序分析（基于房源）
```bash
# 完整的数据收集和分析流程
python src/main.py

# 或使用启动器
python scripts/run_analysis.py main
```

### 小区综合分析
```bash
# 基于小区的综合评分分析
python scripts/run_analysis.py community

# 或直接运行
python scripts/community_analysis/run_community_analysis.py

# 分步执行
python scripts/community_analysis/run_community_analysis.py --step community
python scripts/community_analysis/run_community_analysis.py --step poi
python scripts/community_analysis/run_community_analysis.py --step score
```

### 小区房源聚合分析
```bash
# 从房源数据推导小区特征
python scripts/run_analysis.py housing-aggregate

# 或直接运行
python scripts/community_analysis/run_community_housing_analysis.py
```

## 快速验证

运行项目设置测试：
```bash
python test_setup.py
```

查看示例分析：
```bash
jupyter notebook notebooks/01_quick_start.ipynb
```

## 许可证

MIT License
