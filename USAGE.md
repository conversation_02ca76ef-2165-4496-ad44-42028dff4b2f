# 使用指南

## 快速开始

### 1. 环境准备

```bash
# 安装依赖
pip install -r requirements.txt

# 创建必要目录（如果不存在）
mkdir -p data/raw data/processed data/final outputs logs
```

### 2. 配置API密钥

1. 访问 [高德开放平台](https://console.amap.com/) 申请API密钥
2. 编辑 `config/config.ini` 文件，填入您的API密钥：

```ini
[API_KEYS]
gaode_ak = 您的高德API密钥
```

### 3. 运行项目

#### 方式一：完整流程（推荐）

```bash
python src/main.py
```

这将依次执行：
- 房产数据爬取
- 房产数据清洗和地理编码
- POI数据收集
- 通勤数据计算
- 数据清洗和合并
- 特征工程

#### 方式二：分步执行

```python
# 测试项目设置
python test_setup.py

# 单独运行各模块
from src.data_collection.housing_scraper import LianjiaHousingScraper
scraper = LianjiaHousingScraper()
scraper.run_scraping()
```

### 4. 查看结果

- 原始数据：`data/raw/`
- 处理后数据：`data/processed/`
- 最终数据集：`data/final/final_merged_dataset.csv`
- 日志文件：`cbd_analysis.log`

## 详细说明

### 数据收集

#### 房产数据
- 来源：链家网上海二手房/租房数据
- 范围：浦东新区（可在配置中调整）
- 字段：标题、价格、面积、位置等

#### POI数据
- 来源：高德地图POI搜索API
- 类型：餐饮、购物、医疗、教育、交通等
- 范围：每个小区周边1公里

#### 通勤数据
- 来源：高德地图公交路径规划API
- 目标：陆家嘴CBD核心区域
- 信息：通勤时间、换乘次数、步行距离

### 特征工程

项目会自动创建以下特征：

1. **生活便利度指数**：基于周边POI数量和类型
2. **通勤便捷度指数**：基于到CBD的通勤时间和换乘次数
3. **性价比指数**：综合价格和便利度
4. **宜居指数**：综合多个维度的评分

### 配置选项

#### 爬取参数
```ini
[SCRAPER_PARAMS]
max_pages = 50              # 最大爬取页数
delay_between_requests = 1.0 # 请求间隔（秒）
poi_search_radius_m = 1000   # POI搜索半径（米）
```

#### POI类型
```ini
[GAODE_POI_TYPES]
catering = 050000      # 餐饮服务
shopping = 060000      # 购物服务
medical = 090000       # 医疗保健
education = 140000     # 科教文化
transportation = 150500|150700  # 地铁站|公交站
```

## 注意事项

### API使用限制
- 高德API有每日免费调用次数限制
- 建议设置合理的请求间隔避免被限流
- 大量数据收集可能需要付费API

### 数据质量
- 爬虫可能因网站结构变化而失效
- 地理编码成功率取决于地址质量
- 部分小区可能无法找到通勤路径

### 性能优化
- 可以通过配置跳过已完成的步骤
- 支持断点续传（部分模块）
- 建议在稳定网络环境下运行

## 故障排除

### 常见问题

1. **API密钥错误**
   ```
   错误：高德API密钥未配置或无效
   解决：检查config.ini中的gaode_ak配置
   ```

2. **网络连接问题**
   ```
   错误：网络请求失败
   解决：检查网络连接，增加重试次数
   ```

3. **数据文件不存在**
   ```
   错误：无法加载数据文件
   解决：确保前置步骤已完成，检查文件路径
   ```

### 调试模式

启用详细日志：
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

### 联系支持

如遇到问题，请检查：
1. 项目结构是否完整
2. 依赖包是否正确安装
3. 配置文件是否正确填写
4. 网络连接是否稳定

## 扩展开发

### 添加新的POI类型
1. 在配置文件中添加POI类型编码
2. 在POI数据清洗模块中添加处理逻辑

### 修改分析区域
1. 更新配置文件中的CBD坐标
2. 修改爬虫的目标URL

### 自定义特征
1. 在特征工程模块中添加新的特征计算方法
2. 更新权重配置
