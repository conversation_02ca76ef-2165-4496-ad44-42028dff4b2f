"""
特征工程模块
创建生活便利度指数、通勤便捷度指数等衍生特征
"""

import pandas as pd
import numpy as np
import logging
from typing import Dict, List
from ..utils.config_loader import config
from ..utils.file_io import load_dataframe, save_dataframe, get_data_path

logger = logging.getLogger(__name__)

class FeatureEngineer:
    """特征工程器"""
    
    def __init__(self):
        self.poi_categories = list(config.get_section("GAODE_POI_TYPES").keys())
        
    def create_features(self, input_file: str = None, output_file: str = None) -> str:
        """
        创建特征
        
        Args:
            input_file: 输入文件路径
            output_file: 输出文件路径
            
        Returns:
            输出文件路径
        """
        # 设置文件路径
        if not input_file:
            input_file = config.get("PATHS", "final_dataset",
                                  "data/final/final_merged_dataset.csv")
        
        if not output_file:
            output_file = input_file  # 直接更新原文件
        
        # 加载数据
        df = load_dataframe(get_data_path(input_file))
        if df is None:
            logger.error(f"无法加载数据: {input_file}")
            return ""
        
        logger.info(f"开始特征工程，数据量: {len(df)} 条")
        
        # 创建各种特征
        df = self._create_convenience_index(df)
        df = self._create_commute_index(df)
        df = self._create_price_features(df)
        df = self._create_location_features(df)
        df = self._create_composite_scores(df)
        
        # 保存结果
        output_path = get_data_path(output_file)
        save_dataframe(df, output_path)
        
        logger.info(f"特征工程完成，数据已保存到: {output_path}")
        
        return output_path
    
    def _create_convenience_index(self, df: pd.DataFrame) -> pd.DataFrame:
        """创建生活便利度指数"""
        logger.info("创建生活便利度指数...")
        
        # POI数量得分
        poi_scores = {}
        for category in self.poi_categories:
            count_col = f'{category}_count'
            if count_col in df.columns:
                # 标准化POI数量到0-100分
                max_count = df[count_col].max()
                if max_count > 0:
                    poi_scores[category] = (df[count_col] / max_count * 100).fillna(0)
                else:
                    poi_scores[category] = pd.Series([0] * len(df), index=df.index)
        
        # 计算加权便利度指数
        weights = {
            'catering': 0.20,      # 餐饮
            'shopping': 0.15,      # 购物
            'medical': 0.15,       # 医疗
            'education': 0.10,     # 教育
            'leisure': 0.10,       # 休闲
            'transportation': 0.25, # 交通（权重最高）
            'finance': 0.05        # 金融
        }
        
        convenience_score = pd.Series([0.0] * len(df), index=df.index)
        total_weight = 0
        
        for category, weight in weights.items():
            if category in poi_scores:
                convenience_score += poi_scores[category] * weight
                total_weight += weight
        
        # 标准化到0-100
        if total_weight > 0:
            convenience_score = convenience_score / total_weight
        
        df['convenience_index'] = convenience_score
        
        # 便利度等级
        df['convenience_level'] = pd.cut(
            df['convenience_index'],
            bins=[0, 20, 40, 60, 80, 100],
            labels=['很不便', '不便', '一般', '便利', '很便利']
        )
        
        logger.info(f"生活便利度指数创建完成，平均得分: {convenience_score.mean():.1f}")
        
        return df
    
    def _create_commute_index(self, df: pd.DataFrame) -> pd.DataFrame:
        """创建通勤便捷度指数"""
        logger.info("创建通勤便捷度指数...")
        
        # 通勤时间得分（时间越短得分越高）
        if 'commute_duration_min' in df.columns:
            valid_commute = df['commute_duration_min'].notna()
            
            if valid_commute.sum() > 0:
                max_duration = df.loc[valid_commute, 'commute_duration_min'].max()
                min_duration = df.loc[valid_commute, 'commute_duration_min'].min()
                
                # 时间得分：使用反向线性映射
                time_score = pd.Series([np.nan] * len(df), index=df.index)
                if max_duration > min_duration:
                    time_score.loc[valid_commute] = (
                        100 * (max_duration - df.loc[valid_commute, 'commute_duration_min']) / 
                        (max_duration - min_duration)
                    )
                else:
                    time_score.loc[valid_commute] = 100
                
                df['commute_time_score'] = time_score
        
        # 换乘便利度得分
        if 'commute_transfers' in df.columns:
            transfer_score = pd.Series([np.nan] * len(df), index=df.index)
            valid_transfers = df['commute_transfers'].notna()
            
            if valid_transfers.sum() > 0:
                # 换乘次数得分映射
                transfer_mapping = {0: 100, 1: 85, 2: 70, 3: 55, 4: 40}
                
                for idx in df[valid_transfers].index:
                    transfers = int(df.loc[idx, 'commute_transfers'])
                    transfer_score.loc[idx] = transfer_mapping.get(transfers, max(0, 40 - (transfers - 4) * 10))
                
                df['commute_transfer_score'] = transfer_score
        
        # 综合通勤便捷度指数
        commute_index = pd.Series([np.nan] * len(df), index=df.index)
        
        has_time = 'commute_time_score' in df.columns and df['commute_time_score'].notna()
        has_transfer = 'commute_transfer_score' in df.columns and df['commute_transfer_score'].notna()
        
        if has_time.sum() > 0 or has_transfer.sum() > 0:
            for idx in df.index:
                scores = []
                weights = []
                
                if has_time.loc[idx]:
                    scores.append(df.loc[idx, 'commute_time_score'])
                    weights.append(0.7)  # 时间权重70%
                
                if has_transfer.loc[idx]:
                    scores.append(df.loc[idx, 'commute_transfer_score'])
                    weights.append(0.3)  # 换乘权重30%
                
                if scores:
                    commute_index.loc[idx] = np.average(scores, weights=weights)
        
        df['commute_index'] = commute_index
        
        # 通勤便捷度等级
        df['commute_level'] = pd.cut(
            df['commute_index'],
            bins=[0, 20, 40, 60, 80, 100],
            labels=['很不便', '不便', '一般', '便利', '很便利']
        )
        
        valid_commute_index = df['commute_index'].notna()
        if valid_commute_index.sum() > 0:
            avg_commute_score = df.loc[valid_commute_index, 'commute_index'].mean()
            logger.info(f"通勤便捷度指数创建完成，平均得分: {avg_commute_score:.1f}")
        
        return df
    
    def _create_price_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """创建价格相关特征"""
        logger.info("创建价格特征...")
        
        # 价格性价比指数
        if 'unit_price' in df.columns and 'convenience_index' in df.columns:
            # 计算性价比：便利度/单价的标准化值
            valid_price = df['unit_price'].notna() & df['convenience_index'].notna()
            
            if valid_price.sum() > 0:
                # 标准化单价（取倒数，价格越低越好）
                max_price = df.loc[valid_price, 'unit_price'].max()
                min_price = df.loc[valid_price, 'unit_price'].min()
                
                if max_price > min_price:
                    price_score = 100 * (max_price - df['unit_price']) / (max_price - min_price)
                else:
                    price_score = pd.Series([50] * len(df), index=df.index)
                
                # 性价比 = (便利度得分 + 价格得分) / 2
                df['value_for_money'] = (df['convenience_index'] + price_score) / 2
        
        # 价格区间
        if 'unit_price' in df.columns:
            df['price_range'] = pd.cut(
                df['unit_price'],
                bins=[0, 30000, 50000, 80000, 120000, float('inf')],
                labels=['低价', '中低价', '中价', '中高价', '高价']
            )
        
        return df
    
    def _create_location_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """创建位置相关特征"""
        logger.info("创建位置特征...")
        
        # 到CBD的直线距离
        if 'longitude' in df.columns and 'latitude' in df.columns:
            cbd_lat = config.getfloat("CBD_INFO", "latitude", 31.239638)
            cbd_lng = config.getfloat("CBD_INFO", "longitude", 121.499767)
            
            distances = []
            for _, row in df.iterrows():
                if pd.notna(row['longitude']) and pd.notna(row['latitude']):
                    distance = self._haversine_distance(
                        row['latitude'], row['longitude'], cbd_lat, cbd_lng
                    ) / 1000  # 转换为公里
                    distances.append(distance)
                else:
                    distances.append(np.nan)
            
            df['distance_to_cbd_km'] = distances
            
            # 距离等级
            df['distance_level'] = pd.cut(
                df['distance_to_cbd_km'],
                bins=[0, 5, 10, 15, 25, float('inf')],
                labels=['很近', '较近', '适中', '较远', '很远']
            )
        
        return df
    
    def _create_composite_scores(self, df: pd.DataFrame) -> pd.DataFrame:
        """创建综合评分"""
        logger.info("创建综合评分...")
        
        # 综合宜居指数
        scores = []
        weights = []
        
        if 'convenience_index' in df.columns:
            scores.append(df['convenience_index'])
            weights.append(0.4)  # 便利度40%
        
        if 'commute_index' in df.columns:
            scores.append(df['commute_index'])
            weights.append(0.4)  # 通勤便捷度40%
        
        # 环境得分（基于POI密度，但不过于拥挤）
        if 'convenience_index' in df.columns:
            # 适中的便利度得分更高
            env_score = 100 - abs(df['convenience_index'] - 70)  # 70分为最佳便利度
            env_score = env_score.clip(0, 100)
            scores.append(env_score)
            weights.append(0.2)  # 环境20%
        
        if scores:
            # 计算加权平均
            livability_index = pd.Series([0.0] * len(df), index=df.index)
            total_weight = 0
            
            for i, (score, weight) in enumerate(zip(scores, weights)):
                valid_mask = score.notna()
                livability_index.loc[valid_mask] += score.loc[valid_mask] * weight
                if i == 0:  # 第一次记录有效数据的位置
                    total_weight_series = pd.Series([weight] * len(df), index=df.index)
                    total_weight_series.loc[~valid_mask] = 0
                else:
                    temp_weight = pd.Series([weight] * len(df), index=df.index)
                    temp_weight.loc[~valid_mask] = 0
                    total_weight_series += temp_weight
            
            # 标准化
            valid_total = total_weight_series > 0
            livability_index.loc[valid_total] = (
                livability_index.loc[valid_total] / total_weight_series.loc[valid_total]
            )
            livability_index.loc[~valid_total] = np.nan
            
            df['livability_index'] = livability_index
            
            # 宜居等级
            df['livability_level'] = pd.cut(
                df['livability_index'],
                bins=[0, 20, 40, 60, 80, 100],
                labels=['很差', '较差', '一般', '较好', '很好']
            )
        
        return df
    
    def _haversine_distance(self, lat1: float, lon1: float, lat2: float, lon2: float) -> float:
        """计算两点间距离（米）"""
        from math import radians, cos, sin, asin, sqrt
        
        lat1, lon1, lat2, lon2 = map(radians, [lat1, lon1, lat2, lon2])
        dlat = lat2 - lat1
        dlon = lon2 - lon1
        a = sin(dlat/2)**2 + cos(lat1) * cos(lat2) * sin(dlon/2)**2
        c = 2 * asin(sqrt(a))
        r = 6371000  # 地球半径（米）
        return c * r
