"""
MySQL数据库管理工具
提供数据库连接、表创建、数据存储和读取功能
"""

import pandas as pd
import pymysql
import logging
from sqlalchemy import create_engine, text, MetaData, Table, Column, Integer, String, Float, DateTime, Text, Boolean
from sqlalchemy.orm import sessionmaker
from sqlalchemy.ext.declarative import declarative_base
from typing import Optional, Dict, List, Any
from contextlib import contextmanager
import json
from datetime import datetime

from .config_loader import config

logger = logging.getLogger(__name__)

Base = declarative_base()

class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self):
        """初始化数据库管理器"""
        self.engine = None
        self.Session = None
        self._connect()
    
    def _connect(self):
        """建立数据库连接"""
        try:
            # 获取数据库配置
            host = config.get("DATABASE", "host", "localhost")
            port = config.getint("DATABASE", "port", 3306)
            database = config.get("DATABASE", "database", "cbd_analysis")
            username = config.get("DATABASE", "username", "root")
            password = config.get("DATABASE", "password", "123456")
            charset = config.get("DATABASE", "charset", "utf8mb4")

            # 首先尝试创建数据库（如果不存在）
            try:
                self.create_database_if_not_exists()
            except Exception as db_create_error:
                logger.warning(f"创建数据库时出现警告: {db_create_error}")

            # 创建连接字符串
            connection_string = f"mysql+pymysql://{username}:{password}@{host}:{port}/{database}?charset={charset}"

            # 创建引擎
            self.engine = create_engine(
                connection_string,
                echo=False,  # 设置为True可以看到SQL语句
                pool_pre_ping=True,  # 连接池预检查
                pool_recycle=3600,   # 连接回收时间
                max_overflow=20,     # 最大溢出连接数
                pool_size=10         # 连接池大小
            )

            # 创建Session类
            self.Session = sessionmaker(bind=self.engine)

            # 测试连接
            with self.engine.connect() as conn:
                conn.execute(text("SELECT 1"))

            logger.info(f"数据库连接成功: {host}:{port}/{database}")

        except Exception as e:
            logger.error(f"数据库连接失败: {e}")
            # 如果是数据库不存在的错误，尝试创建数据库
            if "Unknown database" in str(e):
                logger.info("尝试创建数据库...")
                try:
                    self.create_database_if_not_exists()
                    # 重新尝试连接
                    connection_string = f"mysql+pymysql://{username}:{password}@{host}:{port}/{database}?charset={charset}"
                    self.engine = create_engine(connection_string, echo=False, pool_pre_ping=True)
                    self.Session = sessionmaker(bind=self.engine)
                    with self.engine.connect() as conn:
                        conn.execute(text("SELECT 1"))
                    logger.info(f"数据库创建并连接成功: {host}:{port}/{database}")
                except Exception as retry_error:
                    logger.error(f"重试连接失败: {retry_error}")
                    raise
            else:
                raise
    
    @contextmanager
    def get_session(self):
        """获取数据库会话上下文管理器"""
        session = self.Session()
        try:
            yield session
            session.commit()
        except Exception as e:
            session.rollback()
            logger.error(f"数据库操作失败: {e}")
            raise
        finally:
            session.close()
    
    def create_database_if_not_exists(self):
        """创建数据库（如果不存在）"""
        try:
            # 获取配置
            host = config.get("DATABASE", "host", "localhost")
            port = config.getint("DATABASE", "port", 3306)
            database = config.get("DATABASE", "database", "cbd_analysis")
            username = config.get("DATABASE", "username", "root")
            password = config.get("DATABASE", "password", "123456")
            
            # 连接到MySQL服务器（不指定数据库）
            connection = pymysql.connect(
                host=host,
                port=port,
                user=username,
                password=password,
                charset='utf8mb4'
            )
            
            with connection.cursor() as cursor:
                # 创建数据库
                cursor.execute(f"CREATE DATABASE IF NOT EXISTS `{database}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
                logger.info(f"数据库 {database} 创建成功或已存在")
            
            connection.close()
            
        except Exception as e:
            logger.error(f"创建数据库失败: {e}")
            raise
    
    def create_tables(self):
        """创建所有数据表"""
        try:
            # 创建数据库（如果不存在）
            self.create_database_if_not_exists()
            
            # 重新连接到指定数据库
            self._connect()
            
            # 创建所有表
            Base.metadata.create_all(self.engine)
            
            # 创建自定义表
            self._create_custom_tables()
            
            logger.info("数据表创建完成")
            
        except Exception as e:
            logger.error(f"创建数据表失败: {e}")
            raise
    
    def _create_custom_tables(self):
        """创建自定义数据表"""
        with self.engine.connect() as conn:
            # 房产数据表
            conn.execute(text("""
                CREATE TABLE IF NOT EXISTS housing_data (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    housing_id VARCHAR(100) UNIQUE,
                    title VARCHAR(500),
                    community VARCHAR(200),
                    district VARCHAR(100),
                    area VARCHAR(100),
                    address TEXT,
                    total_price DECIMAL(10,2),
                    unit_price DECIMAL(10,2),
                    room_count INT,
                    hall_count INT,
                    bathroom_count INT,
                    floor_info VARCHAR(100),
                    building_area DECIMAL(8,2),
                    building_type VARCHAR(100),
                    building_structure VARCHAR(100),
                    building_orientation VARCHAR(100),
                    decoration VARCHAR(100),
                    elevator VARCHAR(50),
                    property_ownership VARCHAR(100),
                    listing_time DATE,
                    latitude DECIMAL(10,8),
                    longitude DECIMAL(11,8),
                    geocode_level VARCHAR(50),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    INDEX idx_community (community),
                    INDEX idx_district (district),
                    INDEX idx_price (total_price),
                    INDEX idx_location (latitude, longitude)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            """))
            
            # POI数据表
            conn.execute(text("""
                CREATE TABLE IF NOT EXISTS poi_data (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    housing_id VARCHAR(100),
                    poi_type VARCHAR(100),
                    poi_name VARCHAR(200),
                    poi_address TEXT,
                    distance DECIMAL(8,2),
                    latitude DECIMAL(10,8),
                    longitude DECIMAL(11,8),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    INDEX idx_housing_id (housing_id),
                    INDEX idx_poi_type (poi_type),
                    INDEX idx_distance (distance),
                    FOREIGN KEY (housing_id) REFERENCES housing_data(housing_id) ON DELETE CASCADE
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            """))
            
            # POI聚合数据表
            conn.execute(text("""
                CREATE TABLE IF NOT EXISTS poi_aggregated (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    housing_id VARCHAR(100) UNIQUE,
                    catering_count INT DEFAULT 0,
                    catering_avg_distance DECIMAL(8,2),
                    shopping_count INT DEFAULT 0,
                    shopping_avg_distance DECIMAL(8,2),
                    medical_count INT DEFAULT 0,
                    medical_avg_distance DECIMAL(8,2),
                    education_count INT DEFAULT 0,
                    education_avg_distance DECIMAL(8,2),
                    leisure_count INT DEFAULT 0,
                    leisure_avg_distance DECIMAL(8,2),
                    transportation_count INT DEFAULT 0,
                    transportation_avg_distance DECIMAL(8,2),
                    finance_count INT DEFAULT 0,
                    finance_avg_distance DECIMAL(8,2),
                    government_count INT DEFAULT 0,
                    government_avg_distance DECIMAL(8,2),
                    total_poi_count INT DEFAULT 0,
                    poi_density_score DECIMAL(8,4),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    INDEX idx_housing_id (housing_id),
                    FOREIGN KEY (housing_id) REFERENCES housing_data(housing_id) ON DELETE CASCADE
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            """))
            
            # 通勤数据表
            conn.execute(text("""
                CREATE TABLE IF NOT EXISTS commute_data (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    housing_id VARCHAR(100) UNIQUE,
                    origin_latitude DECIMAL(10,8),
                    origin_longitude DECIMAL(11,8),
                    destination_latitude DECIMAL(10,8),
                    destination_longitude DECIMAL(11,8),
                    commute_duration_min INT,
                    commute_distance_km DECIMAL(8,2),
                    commute_cost DECIMAL(8,2),
                    transport_mode VARCHAR(100),
                    route_info TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    INDEX idx_housing_id (housing_id),
                    INDEX idx_duration (commute_duration_min),
                    FOREIGN KEY (housing_id) REFERENCES housing_data(housing_id) ON DELETE CASCADE
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            """))
            
            # 小区聚合数据表
            conn.execute(text("""
                CREATE TABLE IF NOT EXISTS community_aggregated (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    community VARCHAR(200) UNIQUE,
                    district VARCHAR(100),
                    avg_total_price DECIMAL(10,2),
                    avg_unit_price DECIMAL(10,2),
                    property_count INT,
                    avg_building_area DECIMAL(8,2),
                    latitude DECIMAL(10,8),
                    longitude DECIMAL(11,8),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    INDEX idx_community (community),
                    INDEX idx_district (district),
                    INDEX idx_avg_price (avg_total_price)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            """))
            
            conn.commit()
    
    def save_dataframe(self, df: pd.DataFrame, table_name: str, if_exists: str = 'append') -> bool:
        """
        保存DataFrame到数据库表
        
        Args:
            df: DataFrame对象
            table_name: 表名
            if_exists: 如果表存在的处理方式 ('fail', 'replace', 'append')
            
        Returns:
            bool: 是否成功
        """
        try:
            # 处理NaN值
            df_clean = df.copy()
            
            # 将NaN替换为None（数据库NULL）
            df_clean = df_clean.where(pd.notnull(df_clean), None)
            
            # 保存到数据库
            rows_affected = df_clean.to_sql(
                name=table_name,
                con=self.engine,
                if_exists=if_exists,
                index=False,
                method='multi',
                chunksize=1000
            )
            
            logger.info(f"数据已保存到表 {table_name}: {len(df_clean)} 行")
            return True
            
        except Exception as e:
            logger.error(f"保存数据到表 {table_name} 失败: {e}")
            return False
    
    def load_dataframe(self, table_name: str, where_clause: str = None) -> Optional[pd.DataFrame]:
        """
        从数据库表加载DataFrame
        
        Args:
            table_name: 表名
            where_clause: WHERE条件子句
            
        Returns:
            DataFrame对象或None
        """
        try:
            query = f"SELECT * FROM {table_name}"
            if where_clause:
                query += f" WHERE {where_clause}"
            
            df = pd.read_sql(query, self.engine)
            logger.info(f"从表 {table_name} 加载数据: {len(df)} 行")
            return df
            
        except Exception as e:
            logger.error(f"从表 {table_name} 加载数据失败: {e}")
            return None
    
    def execute_query(self, query: str, params: Dict = None) -> Optional[pd.DataFrame]:
        """
        执行自定义SQL查询
        
        Args:
            query: SQL查询语句
            params: 查询参数
            
        Returns:
            DataFrame对象或None
        """
        try:
            df = pd.read_sql(query, self.engine, params=params)
            logger.info(f"查询执行成功，返回 {len(df)} 行")
            return df
            
        except Exception as e:
            logger.error(f"查询执行失败: {e}")
            return None
    
    def table_exists(self, table_name: str) -> bool:
        """检查表是否存在"""
        try:
            with self.engine.connect() as conn:
                result = conn.execute(text(f"SHOW TABLES LIKE '{table_name}'"))
                return result.fetchone() is not None
        except Exception as e:
            logger.error(f"检查表 {table_name} 是否存在失败: {e}")
            return False
    
    def get_table_info(self, table_name: str) -> Dict:
        """获取表信息"""
        try:
            with self.engine.connect() as conn:
                # 获取表结构
                result = conn.execute(text(f"DESCRIBE {table_name}"))
                columns = result.fetchall()
                
                # 获取行数
                result = conn.execute(text(f"SELECT COUNT(*) as count FROM {table_name}"))
                row_count = result.fetchone()[0]
                
                return {
                    'columns': [{'name': col[0], 'type': col[1], 'null': col[2], 'key': col[3]} for col in columns],
                    'row_count': row_count
                }
        except Exception as e:
            logger.error(f"获取表 {table_name} 信息失败: {e}")
            return {}

# 全局数据库管理器实例
db_manager = DatabaseManager()
