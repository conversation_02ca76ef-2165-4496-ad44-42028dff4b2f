"""
小区POI数据聚合模块
将房源级别的POI数据聚合到小区级别，并计算小区POI指数
"""

import pandas as pd
import numpy as np
import logging
from typing import Dict, List
from ..utils.config_loader import config
from ..utils.file_io import load_dataframe, save_dataframe, get_data_path

logger = logging.getLogger(__name__)

class CommunityPOIAggregator:
    """小区POI数据聚合器"""
    
    def __init__(self):
        # POI类别权重配置
        self.poi_weights = {
            'catering': 0.20,      # 餐饮
            'shopping': 0.15,      # 购物
            'medical': 0.15,       # 医疗
            'education': 0.10,     # 教育
            'leisure': 0.10,       # 休闲
            'transportation': 0.25, # 交通（权重最高）
            'finance': 0.05        # 金融
        }
        
        # 获取POI类别配置
        try:
            poi_config = config.get_section("GAODE_POI_TYPES")
            self.poi_categories = list(poi_config.keys())
        except:
            # 默认POI类别
            self.poi_categories = list(self.poi_weights.keys())
    
    def aggregate_community_poi_data(self, housing_file: str = None, poi_file: str = None, 
                                   output_file: str = None) -> str:
        """
        聚合小区POI数据
        
        Args:
            housing_file: 房源数据文件路径
            poi_file: POI数据文件路径
            output_file: 输出文件路径
            
        Returns:
            输出文件路径
        """
        # 设置文件路径
        if not housing_file:
            housing_file = config.get("PATHS", "processed_housing_data",
                                    "data/processed/housing_cleaned_geocoded.csv")
        
        if not poi_file:
            poi_file = config.get("PATHS", "processed_poi_data",
                                "data/processed/poi_gaode_aggregated.csv")
        
        if not output_file:
            output_file = config.get("PATHS", "community_poi_data",
                                   "data/processed/community_poi_aggregated.csv")
        
        # 加载数据
        logger.info("加载房源和POI数据...")
        
        housing_df = load_dataframe(get_data_path(housing_file))
        if housing_df is None:
            logger.error(f"无法加载房源数据: {housing_file}")
            return ""
        
        poi_df = load_dataframe(get_data_path(poi_file))
        if poi_df is None:
            logger.error(f"无法加载POI数据: {poi_file}")
            return ""
        
        logger.info(f"房源数据: {len(housing_df)} 条")
        logger.info(f"POI数据: {len(poi_df)} 条")
        
        # 合并房源和POI数据
        merged_df = self._merge_housing_poi_data(housing_df, poi_df)
        
        # 按小区聚合POI数据
        community_poi_df = self._aggregate_poi_by_community(merged_df)
        
        # 计算小区POI指数
        community_poi_df = self._calculate_community_poi_index(community_poi_df)
        
        # 保存结果
        output_path = get_data_path(output_file)
        save_dataframe(community_poi_df, output_path)
        
        logger.info(f"小区POI数据聚合完成，共 {len(community_poi_df)} 个小区")
        logger.info(f"数据已保存到: {output_path}")
        
        return output_path
    
    def _merge_housing_poi_data(self, housing_df: pd.DataFrame, poi_df: pd.DataFrame) -> pd.DataFrame:
        """合并房源和POI数据"""
        logger.info("合并房源和POI数据...")
        
        # 清理小区名称
        housing_df = self._clean_community_names(housing_df)
        
        # 确保有housing_id列用于合并
        if 'housing_id' not in housing_df.columns:
            housing_df = housing_df.reset_index()
            housing_df.rename(columns={'index': 'housing_id'}, inplace=True)
        
        if 'housing_id' not in poi_df.columns:
            logger.warning("POI数据缺少housing_id列，无法进行精确合并")
            return housing_df
        
        # 合并数据
        merged_df = housing_df.merge(
            poi_df, 
            on='housing_id', 
            how='left',
            suffixes=('', '_poi')
        )
        
        logger.info(f"合并后数据: {len(merged_df)} 条")
        
        return merged_df
    
    def _clean_community_names(self, df: pd.DataFrame) -> pd.DataFrame:
        """清理和标准化小区名称"""
        if 'community' not in df.columns:
            logger.warning("数据中缺少小区字段")
            return df
        
        # 清理小区名称
        df['community_clean'] = df['community'].astype(str).str.strip()
        
        # 去除常见的无效小区名称
        invalid_names = ['nan', 'None', '', '未知', '暂无', '其他']
        df = df[~df['community_clean'].isin(invalid_names)]
        
        # 标准化小区名称（去除多余的描述词）
        df['community_clean'] = df['community_clean'].str.replace(r'（.*?）', '', regex=True)
        df['community_clean'] = df['community_clean'].str.replace(r'\(.*?\)', '', regex=True)
        df['community_clean'] = df['community_clean'].str.replace(r'小区$', '', regex=True)
        df['community_clean'] = df['community_clean'].str.replace(r'公寓$', '', regex=True)
        df['community_clean'] = df['community_clean'].str.strip()
        
        return df
    
    def _aggregate_poi_by_community(self, df: pd.DataFrame) -> pd.DataFrame:
        """按小区聚合POI数据"""
        logger.info("按小区聚合POI数据...")
        
        # 按小区分组
        community_groups = df.groupby('community_clean')
        
        aggregated_data = []
        
        for community_name, group in community_groups:
            community_poi = {
                'community_name': community_name,
                'housing_count': len(group),
            }
            
            # 计算小区坐标（平均值）
            if 'longitude' in group.columns and 'latitude' in group.columns:
                valid_coords = group[['longitude', 'latitude']].dropna()
                if len(valid_coords) > 0:
                    community_poi['longitude'] = valid_coords['longitude'].mean()
                    community_poi['latitude'] = valid_coords['latitude'].mean()
                else:
                    community_poi['longitude'] = np.nan
                    community_poi['latitude'] = np.nan
            
            # 聚合各类POI数据
            for category in self.poi_categories:
                count_col = f'{category}_count'
                rating_col = f'{category}_avg_rating'
                distance_col = f'{category}_avg_distance'
                density_col = f'{category}_density'
                
                # POI数量统计
                if count_col in group.columns:
                    poi_counts = group[count_col].fillna(0)
                    community_poi[f'{category}_total_count'] = poi_counts.sum()
                    community_poi[f'{category}_avg_count'] = poi_counts.mean()
                    community_poi[f'{category}_max_count'] = poi_counts.max()
                else:
                    community_poi[f'{category}_total_count'] = 0
                    community_poi[f'{category}_avg_count'] = 0
                    community_poi[f'{category}_max_count'] = 0
                
                # POI评分统计
                if rating_col in group.columns:
                    ratings = group[rating_col].dropna()
                    if len(ratings) > 0:
                        community_poi[f'{category}_avg_rating'] = ratings.mean()
                        community_poi[f'{category}_max_rating'] = ratings.max()
                    else:
                        community_poi[f'{category}_avg_rating'] = 0
                        community_poi[f'{category}_max_rating'] = 0
                else:
                    community_poi[f'{category}_avg_rating'] = 0
                    community_poi[f'{category}_max_rating'] = 0
                
                # POI距离统计
                if distance_col in group.columns:
                    distances = group[distance_col].dropna()
                    if len(distances) > 0:
                        community_poi[f'{category}_avg_distance'] = distances.mean()
                        community_poi[f'{category}_min_distance'] = distances.min()
                    else:
                        community_poi[f'{category}_avg_distance'] = np.nan
                        community_poi[f'{category}_min_distance'] = np.nan
                else:
                    community_poi[f'{category}_avg_distance'] = np.nan
                    community_poi[f'{category}_min_distance'] = np.nan
                
                # POI密度统计
                if density_col in group.columns:
                    densities = group[density_col].dropna()
                    if len(densities) > 0:
                        community_poi[f'{category}_avg_density'] = densities.mean()
                        community_poi[f'{category}_max_density'] = densities.max()
                    else:
                        community_poi[f'{category}_avg_density'] = 0
                        community_poi[f'{category}_max_density'] = 0
                else:
                    community_poi[f'{category}_avg_density'] = 0
                    community_poi[f'{category}_max_density'] = 0
            
            aggregated_data.append(community_poi)
        
        # 转换为DataFrame
        community_poi_df = pd.DataFrame(aggregated_data)
        
        logger.info(f"POI数据聚合完成，共处理 {len(community_poi_df)} 个小区")
        
        return community_poi_df
    
    def _calculate_community_poi_index(self, df: pd.DataFrame) -> pd.DataFrame:
        """计算小区POI便利度指数"""
        logger.info("计算小区POI便利度指数...")
        
        category_scores = {}
        
        for category, weight in self.poi_weights.items():
            count_col = f'{category}_avg_count'
            density_col = f'{category}_avg_density'
            rating_col = f'{category}_avg_rating'
            
            if count_col in df.columns:
                # 数量得分（标准化到0-100）
                count_score = self._normalize_score(df[count_col])
                
                # 密度得分
                density_score = 0
                if density_col in df.columns:
                    density_score = self._normalize_score(df[density_col])
                
                # 质量得分（基于评分）
                quality_score = 0
                if rating_col in df.columns:
                    quality_score = df[rating_col] * 20  # 5分制转100分制
                
                # 综合该类别得分
                category_score = (count_score * 0.5 + density_score * 0.3 + quality_score * 0.2)
                category_scores[category] = category_score * weight
        
        # 计算总便利度指数
        poi_index = pd.Series([0.0] * len(df), index=df.index)
        
        for category, score in category_scores.items():
            poi_index += score
        
        df['poi_convenience_index'] = poi_index
        
        # 便利度等级
        df['poi_convenience_level'] = pd.cut(
            df['poi_convenience_index'],
            bins=[0, 20, 40, 60, 80, 100],
            labels=['很不便', '不便', '一般', '便利', '很便利']
        )
        
        logger.info(f"POI便利度指数计算完成，平均得分: {poi_index.mean():.1f}")
        
        return df
    
    def _normalize_score(self, series: pd.Series) -> pd.Series:
        """标准化得分到0-100范围"""
        if series.empty or series.max() == series.min():
            return pd.Series([50] * len(series), index=series.index)
        
        min_val = series.min()
        max_val = series.max()
        
        normalized = 100 * (series - min_val) / (max_val - min_val)
        return normalized
