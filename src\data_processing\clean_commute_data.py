"""
通勤数据清洗模块
清洗和标准化通勤时间数据
"""

import pandas as pd
import numpy as np
import logging
from typing import Dict
from ..utils.config_loader import config
from ..utils.file_io import load_dataframe, save_dataframe, get_data_path

logger = logging.getLogger(__name__)

class CommuteDataCleaner:
    """通勤数据清洗器"""
    
    def __init__(self):
        pass
    
    def clean_commute_data(self, input_file: str = None, output_file: str = None) -> str:
        """
        清洗通勤数据
        
        Args:
            input_file: 输入文件路径
            output_file: 输出文件路径
            
        Returns:
            输出文件路径
        """
        # 设置文件路径
        if not input_file:
            input_file = config.get("PATHS", "raw_commute_data",
                                  "data/raw/commute_gaode_to_cbd.csv")
        
        if not output_file:
            output_file = config.get("PATHS", "processed_commute_data",
                                   "data/processed/commute_gaode_cleaned.csv")
        
        # 加载数据
        df = load_dataframe(get_data_path(input_file))
        if df is None:
            logger.error(f"无法加载通勤数据: {input_file}")
            return ""
        
        logger.info(f"开始清洗通勤数据，原始数据: {len(df)} 条")
        
        # 数据清洗步骤
        df = self._clean_numeric_fields(df)
        df = self._handle_missing_routes(df)
        df = self._add_commute_categories(df)
        df = self._calculate_commute_scores(df)
        df = self._filter_valid_records(df)
        
        # 保存清洗后的数据
        output_path = get_data_path(output_file)
        save_dataframe(df, output_path)
        
        logger.info(f"通勤数据清洗完成，清洗后数据: {len(df)} 条")
        logger.info(f"数据已保存到: {output_path}")
        
        # 输出统计信息
        self._print_commute_stats(df)
        
        return output_path
    
    def _clean_numeric_fields(self, df: pd.DataFrame) -> pd.DataFrame:
        """清洗数值字段"""
        numeric_columns = ['commute_duration_min', 'commute_transfers', 'commute_walking_km']
        
        for col in numeric_columns:
            if col in df.columns:
                # 转换为数值类型
                df[col] = pd.to_numeric(df[col], errors='coerce')
                
                # 去除异常值
                if col == 'commute_duration_min':
                    # 通勤时间应在合理范围内
                    df.loc[df[col] < 5, col] = np.nan  # 少于5分钟可能有误
                    df.loc[df[col] > 300, col] = np.nan  # 超过5小时可能有误
                elif col == 'commute_transfers':
                    # 换乘次数应为非负整数
                    df.loc[df[col] < 0, col] = np.nan
                    df.loc[df[col] > 10, col] = np.nan  # 超过10次换乘不太现实
                elif col == 'commute_walking_km':
                    # 步行距离应在合理范围内
                    df.loc[df[col] < 0, col] = np.nan
                    df.loc[df[col] > 20, col] = np.nan  # 超过20公里步行不太现实
        
        logger.debug("通勤数值字段清洗完成")
        return df
    
    def _handle_missing_routes(self, df: pd.DataFrame) -> pd.DataFrame:
        """处理缺失的路径数据"""
        # 标记是否找到路径
        if 'route_found' not in df.columns:
            df['route_found'] = df['commute_duration_min'].notna()
        
        # 对于未找到路径的记录，可以尝试估算
        missing_routes = df['route_found'] == False
        missing_count = missing_routes.sum()
        
        if missing_count > 0:
            logger.info(f"发现 {missing_count} 条记录未找到通勤路径")
            
            # 基于直线距离估算通勤时间（简单估算）
            df = self._estimate_missing_commute_times(df)
        
        return df
    
    def _estimate_missing_commute_times(self, df: pd.DataFrame) -> pd.DataFrame:
        """估算缺失的通勤时间"""
        missing_mask = df['route_found'] == False
        
        if missing_mask.sum() == 0:
            return df
        
        # 计算直线距离
        cbd_lat = config.getfloat("CBD_INFO", "latitude", 31.239638)
        cbd_lng = config.getfloat("CBD_INFO", "longitude", 121.499767)
        
        for idx in df[missing_mask].index:
            try:
                lat = df.loc[idx, 'latitude']
                lng = df.loc[idx, 'longitude']
                
                if pd.notna(lat) and pd.notna(lng):
                    # 计算直线距离（公里）
                    distance_km = self._haversine_distance(lat, lng, cbd_lat, cbd_lng) / 1000
                    
                    # 简单估算：假设平均速度20公里/小时
                    estimated_time = distance_km / 20 * 60  # 转换为分钟
                    
                    # 添加一些随机性和换乘时间
                    estimated_time *= 1.5  # 考虑非直线路径和等车时间
                    
                    df.loc[idx, 'commute_duration_min_estimated'] = estimated_time
                    df.loc[idx, 'commute_transfers_estimated'] = max(1, int(distance_km / 10))  # 每10公里1次换乘
                    df.loc[idx, 'commute_walking_km_estimated'] = min(2.0, distance_km * 0.1)  # 步行距离
                    
            except Exception as e:
                logger.debug(f"估算通勤时间失败 (索引 {idx}): {e}")
                continue
        
        return df
    
    def _haversine_distance(self, lat1: float, lon1: float, lat2: float, lon2: float) -> float:
        """计算两点间距离（米）"""
        from math import radians, cos, sin, asin, sqrt
        
        lat1, lon1, lat2, lon2 = map(radians, [lat1, lon1, lat2, lon2])
        dlat = lat2 - lat1
        dlon = lon2 - lon1
        a = sin(dlat/2)**2 + cos(lat1) * cos(lat2) * sin(dlon/2)**2
        c = 2 * asin(sqrt(a))
        r = 6371000  # 地球半径（米）
        return c * r
    
    def _add_commute_categories(self, df: pd.DataFrame) -> pd.DataFrame:
        """添加通勤分类"""
        if 'commute_duration_min' in df.columns:
            # 通勤时间分类
            df['commute_time_category'] = pd.cut(
                df['commute_duration_min'],
                bins=[0, 30, 45, 60, 90, float('inf')],
                labels=['很近', '较近', '适中', '较远', '很远']
            )
            
            # 通勤便利度分类（综合时间和换乘）
            df['commute_convenience'] = 'unknown'
            
            # 有效通勤数据的记录
            valid_commute = df['route_found'] == True
            
            if valid_commute.sum() > 0:
                # 计算便利度得分
                duration_score = self._normalize_score(df.loc[valid_commute, 'commute_duration_min'], reverse=True)
                transfer_score = self._normalize_score(df.loc[valid_commute, 'commute_transfers'], reverse=True)
                walking_score = self._normalize_score(df.loc[valid_commute, 'commute_walking_km'], reverse=True)
                
                # 综合得分
                convenience_score = (duration_score * 0.5 + transfer_score * 0.3 + walking_score * 0.2)
                
                # 分类
                df.loc[valid_commute, 'commute_convenience'] = pd.cut(
                    convenience_score,
                    bins=[0, 0.2, 0.4, 0.6, 0.8, 1.0],
                    labels=['很不便', '不便', '一般', '便利', '很便利']
                )
        
        logger.debug("通勤分类添加完成")
        return df
    
    def _normalize_score(self, series: pd.Series, reverse: bool = False) -> pd.Series:
        """标准化得分到0-1范围"""
        if series.empty or series.isna().all():
            return series
        
        min_val = series.min()
        max_val = series.max()
        
        if min_val == max_val:
            return pd.Series([0.5] * len(series), index=series.index)
        
        normalized = (series - min_val) / (max_val - min_val)
        
        if reverse:
            normalized = 1 - normalized
        
        return normalized
    
    def _calculate_commute_scores(self, df: pd.DataFrame) -> pd.DataFrame:
        """计算通勤得分"""
        # 通勤时间得分（0-100，时间越短得分越高）
        if 'commute_duration_min' in df.columns:
            valid_duration = df['commute_duration_min'].notna()
            if valid_duration.sum() > 0:
                # 使用反向对数函数计算得分
                max_duration = df.loc[valid_duration, 'commute_duration_min'].max()
                df['commute_time_score'] = np.nan
                
                for idx in df[valid_duration].index:
                    duration = df.loc[idx, 'commute_duration_min']
                    # 得分公式：100 * (1 - log(duration/10) / log(max_duration/10))
                    if duration > 0:
                        score = max(0, 100 * (1 - np.log(duration/10) / np.log(max_duration/10)))
                        df.loc[idx, 'commute_time_score'] = min(100, score)
        
        # 换乘便利度得分
        if 'commute_transfers' in df.columns:
            df['transfer_score'] = np.nan
            valid_transfers = df['commute_transfers'].notna()
            
            if valid_transfers.sum() > 0:
                # 换乘次数越少得分越高
                transfer_scores = {0: 100, 1: 85, 2: 70, 3: 55, 4: 40}
                
                for idx in df[valid_transfers].index:
                    transfers = int(df.loc[idx, 'commute_transfers'])
                    df.loc[idx, 'transfer_score'] = transfer_scores.get(transfers, max(0, 40 - (transfers - 4) * 10))
        
        logger.debug("通勤得分计算完成")
        return df
    
    def _filter_valid_records(self, df: pd.DataFrame) -> pd.DataFrame:
        """过滤有效记录"""
        original_count = len(df)
        
        # 必须有坐标信息
        has_coords = df[['longitude', 'latitude']].notna().all(axis=1)
        df = df[has_coords]
        
        filtered_count = original_count - len(df)
        if filtered_count > 0:
            logger.info(f"过滤无效记录: {filtered_count} 条")
        
        return df
    
    def _print_commute_stats(self, df: pd.DataFrame):
        """打印通勤统计信息"""
        logger.info("通勤数据统计:")
        
        total_count = len(df)
        valid_routes = df['route_found'].sum() if 'route_found' in df.columns else 0
        
        logger.info(f"  总记录数: {total_count}")
        logger.info(f"  有效路径: {valid_routes} ({valid_routes/total_count*100:.1f}%)")
        
        if valid_routes > 0:
            valid_df = df[df['route_found'] == True]
            
            if 'commute_duration_min' in valid_df.columns:
                avg_duration = valid_df['commute_duration_min'].mean()
                min_duration = valid_df['commute_duration_min'].min()
                max_duration = valid_df['commute_duration_min'].max()
                
                logger.info(f"  平均通勤时间: {avg_duration:.1f} 分钟")
                logger.info(f"  最短通勤时间: {min_duration:.1f} 分钟")
                logger.info(f"  最长通勤时间: {max_duration:.1f} 分钟")
            
            if 'commute_transfers' in valid_df.columns:
                avg_transfers = valid_df['commute_transfers'].mean()
                logger.info(f"  平均换乘次数: {avg_transfers:.1f} 次")
            
            if 'commute_time_category' in df.columns:
                category_counts = df['commute_time_category'].value_counts()
                logger.info("  通勤时间分布:")
                for category, count in category_counts.items():
                    logger.info(f"    {category}: {count} ({count/total_count*100:.1f}%)")
