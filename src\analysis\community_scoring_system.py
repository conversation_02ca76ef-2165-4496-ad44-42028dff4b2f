"""
小区综合评分系统
整合POI便利度、通勤便捷度等多维度数据，为每个小区计算综合评分
"""

import pandas as pd
import numpy as np
import logging
from typing import Dict, Optional
from ..utils.config_loader import config
from ..utils.file_io import load_dataframe, save_dataframe, get_data_path

logger = logging.getLogger(__name__)

class CommunityScoringSystem:
    """小区综合评分系统"""
    
    def __init__(self):
        # 各维度权重配置
        self.dimension_weights = self._load_dimension_weights()
        
        # 评分范围
        self.score_range = (0, 100)
        
        logger.info(f"评分维度权重: {self.dimension_weights}")
    
    def _load_dimension_weights(self) -> Dict[str, float]:
        """加载各维度权重配置"""
        # 可以从配置文件读取，这里使用默认权重
        weights = {
            'commute_convenience': 0.35,    # 通勤便捷度权重最高
            'poi_convenience': 0.30,        # POI便利度
            'location_advantage': 0.20,     # 位置优势（距离CBD）
            'environment_quality': 0.15     # 环境质量（可扩展）
        }
        
        # 确保权重总和为1
        total_weight = sum(weights.values())
        if total_weight != 1.0:
            weights = {k: v / total_weight for k, v in weights.items()}
            logger.warning(f"权重已标准化，总和: {total_weight} -> 1.0")
        
        return weights
    
    def calculate_comprehensive_scores(self, 
                                     community_file: str = None,
                                     poi_file: str = None,
                                     commute_file: str = None) -> str:
        """
        计算小区综合评分
        
        Args:
            community_file: 小区基础数据文件
            poi_file: POI便利度数据文件
            commute_file: 通勤便捷度数据文件
            
        Returns:
            保存的综合评分文件路径
        """
        logger.info("开始计算小区综合评分...")
        
        # 加载各维度数据
        data_dict = self._load_all_data(community_file, poi_file, commute_file)
        
        if not data_dict:
            logger.error("无法加载必要的数据文件")
            return ""
        
        # 合并数据
        merged_df = self._merge_data(data_dict)
        
        if merged_df.empty:
            logger.error("数据合并后为空")
            return ""
        
        # 计算各维度得分
        scored_df = self._calculate_dimension_scores(merged_df)
        
        # 计算综合得分
        final_df = self._calculate_comprehensive_score(scored_df)
        
        # 添加排名和等级
        final_df = self._add_rankings_and_levels(final_df)
        
        # 保存结果
        output_file = config.get("PATHS", "final_scores",
                                "data/final/community_comprehensive_scores.csv")
        output_path = get_data_path(output_file)
        
        save_dataframe(final_df, output_path)
        
        logger.info(f"小区综合评分计算完成: {len(final_df)} 个小区")
        logger.info(f"结果已保存到: {output_path}")
        
        # 输出统计信息
        self._print_scoring_stats(final_df)
        
        return output_path
    
    def _load_all_data(self, community_file: str, poi_file: str, commute_file: str) -> Dict[str, pd.DataFrame]:
        """加载所有数据文件"""
        data_dict = {}
        
        # 加载小区基础数据
        if not community_file:
            community_file = config.get("PATHS", "raw_housing_data",
                                      "data/raw/communities_lujiazui.csv")
        
        community_df = load_dataframe(get_data_path(community_file))
        if community_df is not None:
            data_dict['community'] = community_df
            logger.info(f"加载小区数据: {len(community_df)} 条记录")
        else:
            logger.error(f"无法加载小区数据: {community_file}")
            return {}
        
        # 加载POI数据
        if not poi_file:
            poi_file = config.get("PATHS", "processed_poi_data",
                                "data/processed/community_poi_scores.csv")
        
        poi_df = load_dataframe(get_data_path(poi_file))
        if poi_df is not None:
            data_dict['poi'] = poi_df
            logger.info(f"加载POI数据: {len(poi_df)} 条记录")
        else:
            logger.warning(f"无法加载POI数据: {poi_file}")
        
        # 加载通勤数据
        if not commute_file:
            commute_file = config.get("PATHS", "processed_commute_data",
                                    "data/processed/community_commute_scores.csv")
        
        commute_df = load_dataframe(get_data_path(commute_file))
        if commute_df is not None:
            data_dict['commute'] = commute_df
            logger.info(f"加载通勤数据: {len(commute_df)} 条记录")
        else:
            logger.warning(f"无法加载通勤数据: {commute_file}")
        
        return data_dict
    
    def _merge_data(self, data_dict: Dict[str, pd.DataFrame]) -> pd.DataFrame:
        """合并各维度数据"""
        logger.info("合并各维度数据...")
        
        # 以小区数据为基础
        merged_df = data_dict['community'].copy()
        
        # 合并POI数据
        if 'poi' in data_dict:
            poi_df = data_dict['poi']
            merged_df = merged_df.merge(
                poi_df, 
                on='community_name', 
                how='left',
                suffixes=('', '_poi')
            )
            logger.info(f"合并POI数据后: {len(merged_df)} 条记录")
        
        # 合并通勤数据
        if 'commute' in data_dict:
            commute_df = data_dict['commute']
            merged_df = merged_df.merge(
                commute_df,
                on='community_name',
                how='left',
                suffixes=('', '_commute')
            )
            logger.info(f"合并通勤数据后: {len(merged_df)} 条记录")
        
        # 只保留有坐标的小区
        valid_df = merged_df[
            merged_df['longitude'].notna() & 
            merged_df['latitude'].notna()
        ].copy()
        
        logger.info(f"有效小区数据: {len(valid_df)} 条记录")
        
        return valid_df
    
    def _calculate_dimension_scores(self, df: pd.DataFrame) -> pd.DataFrame:
        """计算各维度得分"""
        logger.info("计算各维度得分...")
        
        result_df = df.copy()
        
        # 1. 通勤便捷度得分
        if 'commute_convenience_index' in df.columns:
            result_df['commute_score'] = df['commute_convenience_index'].fillna(0)
        else:
            # 如果没有通勤数据，基于距离估算
            result_df['commute_score'] = self._estimate_commute_score_by_distance(df)
        
        # 2. POI便利度得分
        if 'convenience_index' in df.columns:
            result_df['poi_score'] = df['convenience_index'].fillna(0)
        else:
            # 如果没有POI数据，给默认分数
            result_df['poi_score'] = 50  # 中等分数
        
        # 3. 位置优势得分
        result_df['location_score'] = self._calculate_location_score(df)
        
        # 4. 环境质量得分（可扩展）
        result_df['environment_score'] = self._calculate_environment_score(df)
        
        logger.info("各维度得分计算完成")
        
        return result_df
    
    def _estimate_commute_score_by_distance(self, df: pd.DataFrame) -> pd.Series:
        """基于距离估算通勤得分"""
        if 'distance_to_cbd_km' not in df.columns:
            return pd.Series([50] * len(df), index=df.index)
        
        distances = df['distance_to_cbd_km'].fillna(df['distance_to_cbd_km'].mean())
        
        # 距离越近得分越高
        max_distance = distances.max()
        min_distance = distances.min()
        
        if max_distance > min_distance:
            score = 100 * (max_distance - distances) / (max_distance - min_distance)
        else:
            score = pd.Series([75] * len(df), index=df.index)
        
        return score
    
    def _calculate_location_score(self, df: pd.DataFrame) -> pd.Series:
        """计算位置优势得分"""
        if 'distance_to_cbd_km' not in df.columns:
            return pd.Series([50] * len(df), index=df.index)
        
        distances = df['distance_to_cbd_km'].fillna(df['distance_to_cbd_km'].mean())
        
        # 使用非线性函数，距离CBD越近得分越高
        # 3公里内高分，超过10公里低分
        score = pd.Series(index=df.index, dtype=float)
        
        for idx, distance in distances.items():
            if distance <= 3:
                score[idx] = 100 - distance * 5  # 3公里内：85-100分
            elif distance <= 6:
                score[idx] = 85 - (distance - 3) * 10  # 3-6公里：55-85分
            elif distance <= 10:
                score[idx] = 55 - (distance - 6) * 8  # 6-10公里：23-55分
            else:
                score[idx] = max(10, 23 - (distance - 10) * 2)  # 10公里以上：10-23分
        
        return score
    
    def _calculate_environment_score(self, df: pd.DataFrame) -> pd.Series:
        """计算环境质量得分（可扩展）"""
        # 目前基于位置给出基础环境分数
        # 未来可以集成空气质量、噪音、绿化等数据
        
        base_score = 60  # 基础环境分数
        
        # 基于距离CBD的环境调整
        if 'distance_to_cbd_km' in df.columns:
            distances = df['distance_to_cbd_km'].fillna(df['distance_to_cbd_km'].mean())
            
            # 距离CBD适中的区域环境更好
            environment_score = pd.Series(index=df.index, dtype=float)
            
            for idx, distance in distances.items():
                if 2 <= distance <= 8:  # 2-8公里环境较好
                    environment_score[idx] = base_score + 20
                elif distance < 2:  # 太近可能嘈杂
                    environment_score[idx] = base_score + 10
                elif distance <= 12:  # 较远但还可以
                    environment_score[idx] = base_score
                else:  # 太远
                    environment_score[idx] = base_score - 10
            
            return environment_score
        else:
            return pd.Series([base_score] * len(df), index=df.index)
    
    def _calculate_comprehensive_score(self, df: pd.DataFrame) -> pd.DataFrame:
        """计算综合得分"""
        logger.info("计算综合得分...")
        
        result_df = df.copy()
        
        # 计算加权综合得分
        comprehensive_score = pd.Series([0.0] * len(df), index=df.index)
        
        score_columns = {
            'commute_score': 'commute_convenience',
            'poi_score': 'poi_convenience', 
            'location_score': 'location_advantage',
            'environment_score': 'environment_quality'
        }
        
        for score_col, weight_key in score_columns.items():
            if score_col in df.columns:
                weight = self.dimension_weights.get(weight_key, 0)
                comprehensive_score += df[score_col] * weight
        
        result_df['comprehensive_score'] = comprehensive_score
        
        # 确保分数在合理范围内
        result_df['comprehensive_score'] = result_df['comprehensive_score'].clip(
            self.score_range[0], self.score_range[1]
        )
        
        avg_score = result_df['comprehensive_score'].mean()
        logger.info(f"综合得分计算完成，平均分: {avg_score:.1f}")
        
        return result_df
    
    def _add_rankings_and_levels(self, df: pd.DataFrame) -> pd.DataFrame:
        """添加排名和等级"""
        logger.info("添加排名和等级...")
        
        result_df = df.copy()
        
        # 综合得分排名
        result_df['comprehensive_rank'] = result_df['comprehensive_score'].rank(
            method='dense', ascending=False
        ).astype(int)
        
        # 综合等级
        result_df['comprehensive_level'] = pd.cut(
            result_df['comprehensive_score'],
            bins=[0, 40, 55, 70, 85, 100],
            labels=['较差', '一般', '良好', '优秀', '卓越']
        )
        
        # 各维度排名
        dimension_scores = ['commute_score', 'poi_score', 'location_score', 'environment_score']
        
        for score_col in dimension_scores:
            if score_col in result_df.columns:
                rank_col = score_col.replace('_score', '_rank')
                result_df[rank_col] = result_df[score_col].rank(
                    method='dense', ascending=False
                ).astype(int)
        
        # 添加推荐标签
        result_df['recommendation'] = self._generate_recommendations(result_df)
        
        return result_df
    
    def _generate_recommendations(self, df: pd.DataFrame) -> pd.Series:
        """生成推荐标签"""
        recommendations = pd.Series([''] * len(df), index=df.index)
        
        for idx, row in df.iterrows():
            tags = []
            
            # 基于综合得分
            if row['comprehensive_score'] >= 85:
                tags.append('强烈推荐')
            elif row['comprehensive_score'] >= 70:
                tags.append('推荐')
            
            # 基于通勤便利性
            if 'commute_score' in row and row['commute_score'] >= 80:
                tags.append('通勤便利')
            
            # 基于生活便利性
            if 'poi_score' in row and row['poi_score'] >= 80:
                tags.append('生活便利')
            
            # 基于位置优势
            if 'location_score' in row and row['location_score'] >= 85:
                tags.append('位置优越')
            
            recommendations[idx] = ', '.join(tags) if tags else '一般'
        
        return recommendations
    
    def _print_scoring_stats(self, df: pd.DataFrame):
        """打印评分统计信息"""
        logger.info("综合评分统计:")
        
        logger.info(f"  总小区数: {len(df)}")
        
        if 'comprehensive_score' in df.columns:
            avg_score = df['comprehensive_score'].mean()
            max_score = df['comprehensive_score'].max()
            min_score = df['comprehensive_score'].min()
            
            logger.info(f"  平均综合得分: {avg_score:.1f}")
            logger.info(f"  最高得分: {max_score:.1f}")
            logger.info(f"  最低得分: {min_score:.1f}")
        
        if 'comprehensive_level' in df.columns:
            level_counts = df['comprehensive_level'].value_counts()
            logger.info("  等级分布:")
            for level, count in level_counts.items():
                percentage = count / len(df) * 100
                logger.info(f"    {level}: {count} ({percentage:.1f}%)")
    
    def get_top_communities(self, score_file: str = None, top_n: int = 10) -> pd.DataFrame:
        """获取评分最高的小区"""
        if not score_file:
            score_file = config.get("PATHS", "final_scores",
                                  "data/final/community_comprehensive_scores.csv")
        
        try:
            df = load_dataframe(get_data_path(score_file))
            if df is None:
                return pd.DataFrame()
            
            # 按综合得分排序
            top_df = df.nlargest(top_n, 'comprehensive_score')
            
            # 选择关键列
            key_columns = [
                'community_name', 'comprehensive_score', 'comprehensive_rank',
                'comprehensive_level', 'commute_score', 'poi_score', 
                'location_score', 'distance_to_cbd_km', 'recommendation'
            ]
            
            available_columns = [col for col in key_columns if col in top_df.columns]
            
            return top_df[available_columns]
            
        except Exception as e:
            logger.error(f"获取顶级小区失败: {e}")
            return pd.DataFrame()
