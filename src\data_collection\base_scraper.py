"""
基础爬虫类
提供通用的网页爬取功能
"""

import requests
import time
import logging
import random
from typing import Optional
from bs4 import BeautifulSoup
from urllib.parse import urljoin

logger = logging.getLogger(__name__)

class BaseScraper:
    """基础爬虫类"""
    
    def __init__(self, delay: float = 1.0):
        """
        初始化基础爬虫
        
        Args:
            delay: 请求间隔时间（秒）
        """
        self.delay = delay
        self.session = requests.Session()
        
        # 设置请求头
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })
    
    def get_page(self, url: str, timeout: int = 10) -> Optional[BeautifulSoup]:
        """
        获取页面内容
        
        Args:
            url: 目标URL
            timeout: 超时时间
            
        Returns:
            BeautifulSoup对象或None
        """
        try:
            logger.debug(f"请求页面: {url}")
            
            response = self.session.get(url, timeout=timeout)
            response.raise_for_status()
            
            # 检测编码
            response.encoding = response.apparent_encoding
            
            # 解析HTML
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 添加延时
            time.sleep(random.uniform(self.delay * 0.5, self.delay * 1.5))
            
            return soup
            
        except requests.RequestException as e:
            logger.error(f"请求失败: {url}, 错误: {e}")
            return None
        except Exception as e:
            logger.error(f"页面解析失败: {url}, 错误: {e}")
            return None
    
    def extract_text(self, element) -> str:
        """
        安全提取元素文本
        
        Args:
            element: BeautifulSoup元素
            
        Returns:
            文本内容
        """
        if element is None:
            return ""
        
        try:
            return element.get_text(strip=True)
        except Exception:
            return ""
    
    def extract_attribute(self, element, attr: str) -> str:
        """
        安全提取元素属性
        
        Args:
            element: BeautifulSoup元素
            attr: 属性名
            
        Returns:
            属性值
        """
        if element is None:
            return ""
        
        try:
            return element.get(attr, "")
        except Exception:
            return ""
    
    def build_absolute_url(self, base_url: str, relative_url: str) -> str:
        """
        构建绝对URL
        
        Args:
            base_url: 基础URL
            relative_url: 相对URL
            
        Returns:
            绝对URL
        """
        try:
            return urljoin(base_url, relative_url)
        except Exception:
            return relative_url
    
    def close(self):
        """关闭会话"""
        try:
            self.session.close()
        except Exception as e:
            logger.error(f"关闭会话失败: {e}")
