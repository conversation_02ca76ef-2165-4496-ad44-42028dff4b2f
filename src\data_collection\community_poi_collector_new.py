import pandas as pd
import requests
import json
import time
import logging
from pathlib import Path

# Assuming config_loader and logger_setup are in src.utils
from src.utils.config_loader import ConfigLoader
from src.utils.logger_setup import setup_logger

# Setup logger
logger = setup_logger()

# Load configuration
config = ConfigLoader()
API_KEY = config.get("API_KEYS", "gaode_ak")
DEFAULT_RADIUS = config.getint("SCRAPER_PARAMS", "poi_search_radius_m", 1000)
# Default POI types as per task description, can be made configurable later
DEFAULT_POI_TYPES = "050000|060000|070000|080000|090000|100000|110000|120000|150000|170200"
PAGE_SIZE = 25  # Max allowed by API
GAODE_API_URL = "https://restapi.amap.com/v5/place/around"

# Output file path
OUTPUT_DIR = Path(config.get("PATHS", "raw_poi_data", "data/raw/community_gaode_poi_raw.json")).parent
OUTPUT_FILENAME = Path(config.get("PATHS", "raw_poi_data", "data/raw/community_gaode_poi_raw.json")).name
OUTPUT_FILE = OUTPUT_DIR / OUTPUT_FILENAME


def fetch_pois_for_location(location_coords, community_name, api_key, radius, types, page_size):
    """
    Fetches POIs for a given location from Gaode API, handling pagination.

    Args:
        location_coords (str): "longitude,latitude"
        community_name (str): Name of the community for logging.
        api_key (str): Gaode API key.
        radius (int): Search radius in meters.
        types (str): POI type codes, separated by '|'.
        page_size (int): Number of results per page.

    Returns:
        tuple: (list of POIs, total_retrieved_count, pages_fetched)
               Returns (None, 0, 0) if a critical error occurs.
    """
    all_pois = []
    page_num = 1
    total_retrieved_count = 0
    pages_fetched = 0
    max_retries = 3
    retry_delay = 5  # seconds

    while True:
        params = {
            "key": api_key,
            "location": location_coords,
            "radius": radius,
            "types": types,
            "page_size": page_size,
            "page_num": page_num,
            "show_fields": "pois" # Ensure we get detailed POI info
        }
        
        for attempt in range(max_retries):
            try:
                logger.info(f"Fetching POIs for {community_name} ({location_coords}), page {page_num}, attempt {attempt + 1}")
                response = requests.get(GAODE_API_URL, params=params, timeout=20)
                response.raise_for_status()  # Raise HTTPError for bad responses (4xx or 5xx)
                data = response.json()
                pages_fetched +=1

                if data.get("status") == "1":
                    pois_in_page = data.get("pois", [])
                    count_str = data.get("count", "0")
                    
                    try:
                        current_total_expected = int(count_str)
                    except ValueError:
                        logger.error(f"Invalid 'count' value received from API for {community_name}: {count_str}")
                        current_total_expected = 0 # Treat as no results if count is invalid

                    if not pois_in_page and page_num == 1 and current_total_expected == 0:
                        logger.info(f"No POIs found for {community_name} ({location_coords}) with types {types}.")
                        return [], 0, pages_fetched # No POIs found, but request was successful

                    all_pois.extend(pois_in_page)
                    total_retrieved_count += len(pois_in_page)
                    
                    logger.debug(f"Page {page_num}: Fetched {len(pois_in_page)} POIs. Total so far: {total_retrieved_count}. API reports total: {current_total_expected}")

                    if not pois_in_page or total_retrieved_count >= current_total_expected or current_total_expected == 0:
                        logger.info(f"Finished fetching for {community_name}. Total POIs: {total_retrieved_count}, Pages: {pages_fetched}")
                        return all_pois, total_retrieved_count, pages_fetched
                    
                    page_num += 1
                    time.sleep(0.1) # Small delay between pages
                    break # Success, exit retry loop

                elif data.get("status") == "0":
                    # API returned an error status
                    error_info = data.get("info", "Unknown API error")
                    infocode = data.get("infocode", "N/A")
                    logger.error(f"Gaode API error for {community_name} ({location_coords}), page {page_num}: {error_info} (Infocode: {infocode})")
                    # Specific error codes that might indicate no more data or a non-retryable issue
                    if infocode in ["10001", "10002", "10003", "10004", "10009", "10013", "20000", "20001", "20002", "20003", "20800", "20801", "20802", "20803", "30001"]: # Invalid key, quota, no results etc.
                        if page_num == 1: # Error on the first page
                             return None, 0, pages_fetched # Critical error on first page
                        else: # Error on subsequent pages, might mean no more data
                             logger.warning(f"API error on page {page_num} for {community_name}, assuming no more data. Partial results: {total_retrieved_count} POIs.")
                             return all_pois, total_retrieved_count, pages_fetched
                    # For other errors, retry
                    if attempt == max_retries - 1:
                        logger.error(f"Max retries reached for {community_name}, page {page_num}. API error: {error_info}")
                        return all_pois, total_retrieved_count, pages_fetched # Return what we have so far
                    time.sleep(retry_delay * (attempt + 1)) # Exponential backoff
                else:
                    # Unexpected status
                    logger.error(f"Unexpected API status for {community_name} ({location_coords}), page {page_num}: {data.get('status')}, Info: {data.get('info')}")
                    if attempt == max_retries - 1:
                        return all_pois, total_retrieved_count, pages_fetched
                    time.sleep(retry_delay * (attempt + 1))

            except requests.exceptions.RequestException as e:
                logger.error(f"Network error fetching POIs for {community_name} ({location_coords}), page {page_num}, attempt {attempt + 1}: {e}")
                if attempt == max_retries - 1:
                    logger.error(f"Max retries reached for network error for {community_name}, page {page_num}.")
                    return all_pois, total_retrieved_count, pages_fetched # Return what we have so far
                time.sleep(retry_delay * (attempt + 1)) # Exponential backoff
            except json.JSONDecodeError as e:
                logger.error(f"JSON decode error for {community_name} ({location_coords}), page {page_num}: {e}. Response: {response.text[:200]}")
                return None, 0, pages_fetched # Cannot parse, critical error
            except Exception as e:
                logger.error(f"An unexpected error occurred for {community_name} ({location_coords}), page {page_num}: {e}")
                return None, 0, pages_fetched # Unexpected critical error
    
    return all_pois, total_retrieved_count, pages_fetched # Should be reached if loop breaks unexpectedly


def main():
    logger.info("Starting POI data collection process.")
    
    # Ensure output directory exists
    OUTPUT_DIR.mkdir(parents=True, exist_ok=True)

    # --- Step 1: Load Geocoded Community Data ---
    # Define the path to the geocoded communities CSV file
    # This path is specified in the task requirements.
    geocoded_communities_csv_path = Path("data/processed/communities_geocoded.csv")

    try:
        if not geocoded_communities_csv_path.exists():
            logger.error(f"Input file not found: {geocoded_communities_csv_path}")
            return
        communities_df = pd.read_csv(geocoded_communities_csv_path)
        logger.info(f"Successfully loaded {len(communities_df)} communities from {geocoded_communities_csv_path}")
    except Exception as e:
        logger.error(f"Error loading {geocoded_communities_csv_path}: {e}")
        return

    # Expected columns: community_name, longitude, latitude
    required_columns = ['community_name', 'longitude', 'latitude']
    if not all(col in communities_df.columns for col in required_columns):
        logger.error(f"Missing one or more required columns ({', '.join(required_columns)}) in {geocoded_communities_csv_path}. Found columns: {', '.join(communities_df.columns)}")
        return
        
    all_community_poi_data = []
    
    # --- Step 2: Implement Collection Logic ---
    if not API_KEY: # This check was already present, keeping it
        logger.error("Gaode API Key (gaode_ak) not found in config.ini. Exiting.")
        return

    for index, row in communities_df.iterrows():
        community_name = str(row["community_name"]) # Ensure community name is string
        longitude = row["longitude"]
        latitude = row["latitude"]

        # Validate coordinates: check for NaN, None, or empty strings
        if pd.isna(longitude) or str(longitude).strip() == "" or \
           pd.isna(latitude) or str(latitude).strip() == "":
            logger.warning(f"Skipping community '{community_name}' (CSV Row {index+2}) due to missing coordinates (lon: '{longitude}', lat: '{latitude}').")
            continue
        
        try:
            # Attempt to convert to float and format. Gaode API uses up to 6 decimal places.
            location_coords = f"{float(longitude):.6f},{float(latitude):.6f}"
        except ValueError:
            logger.warning(f"Skipping community '{community_name}' (CSV Row {index+2}) due to non-numeric or invalid format coordinates (lon: '{longitude}', lat: '{latitude}').")
            continue
            
        logger.info(f"Processing community: {community_name} with location: {location_coords}")
        
        poi_data, retrieved_count, pages = fetch_pois_for_location(
            location_coords=location_coords,
            community_name=community_name,
            api_key=API_KEY,
            radius=DEFAULT_RADIUS,
            types=DEFAULT_POI_TYPES,
            page_size=PAGE_SIZE
        )
        
        if poi_data is not None:
            community_result = {
                "community_name": community_name,
                "community_location": location_coords,    # Coords string sent to API "lon,lat"
                "source_longitude": longitude,            # Original longitude from CSV
                "source_latitude": latitude,              # Original latitude from CSV
                "retrieved_pois_count": retrieved_count,
                "pages_fetched": pages,
                "poi_data": poi_data
            }
            all_community_poi_data.append(community_result)
            logger.info(f"Successfully collected {retrieved_count} POIs for {community_name} over {pages} pages.")
        else:
            logger.error(f"Failed to collect POIs for {community_name} (Location: {location_coords}) after multiple retries or due to critical API error.")
            community_result = {
                "community_name": community_name,
                "community_location": location_coords, # location_coords should be defined if fetch_pois_for_location was called
                "source_longitude": longitude,
                "source_latitude": latitude,
                "retrieved_pois_count": 0,
                "pages_fetched": pages,
                "poi_data": [],
                "error": "Failed to retrieve POIs"
            }
            all_community_poi_data.append(community_result)

        time.sleep(1) # Rate limiting between different communities

    # Store collected data
    try:
        with open(OUTPUT_FILE, 'w', encoding='utf-8') as f:
            json.dump(all_community_poi_data, f, ensure_ascii=False, indent=4)
        logger.info(f"Successfully saved POI data to {OUTPUT_FILE}")
    except IOError as e:
        logger.error(f"Error saving POI data to {OUTPUT_FILE}: {e}")

    logger.info("POI data collection process finished.")

if __name__ == "__main__":
    main()