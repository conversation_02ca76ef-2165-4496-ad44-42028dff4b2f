"""
房产数据清洗模块
清洗和标准化房产数据，包括地理编码
"""

import pandas as pd
import numpy as np
import re
import logging
from typing import Dict, List, Optional
from ..utils.config_loader import config
from ..utils.file_io import load_dataframe, save_dataframe, load_dataframe_hybrid, save_dataframe_hybrid, get_data_path
from ..utils.api_helpers import batch_geocode_addresses

logger = logging.getLogger(__name__)

class HousingDataCleaner:
    """房产数据清洗器"""
    
    def __init__(self):
        self.required_columns = ['title']  # 移除type要求，只要求有title
        self.numeric_columns = ['total_price', 'unit_price', 'area_numeric']  # 移除rental_price，添加area_numeric
        
    def clean_housing_data(self, input_file: str = None, output_file: str = None) -> str:
        """
        清洗房产数据
        
        Args:
            input_file: 输入文件路径
            output_file: 输出文件路径
            
        Returns:
            输出文件路径
        """
        # 设置文件路径
        if not input_file:
            input_file = config.get("PATHS", "raw_housing_data",
                                  "data/raw/housing_listings_lujiazui.csv")
        
        if not output_file:
            output_file = config.get("PATHS", "processed_housing_data",
                                   "data/processed/housing_cleaned_geocoded.csv")
        
        # 加载数据：优先从文件加载，如果失败则从数据库加载
        df = load_dataframe_hybrid(
            filepath=get_data_path(input_file),
            table_name='housing_data',
            prefer_db=False  # 改为优先从文件加载
        )
        if df is None:
            logger.error(f"无法加载房产数据: {input_file}")
            return ""
        
        logger.info(f"开始清洗房产数据，原始数据: {len(df)} 条")
        
        # 数据清洗步骤
        df = self._remove_duplicates(df)
        df = self._extract_community_info(df)
        df = self._clean_text_fields(df)
        df = self._clean_numeric_fields(df)
        df = self._standardize_area_info(df)
        df = self._geocode_addresses(df)
        df = self._add_derived_fields(df)
        df = self._filter_valid_records(df)
        
        # 保存清洗后的数据：同时保存到文件和数据库
        output_path = get_data_path(output_file)
        save_dataframe_hybrid(
            df=df,
            filepath=output_path,
            table_name='housing_data_cleaned',
            if_exists='replace'  # 清洗后的数据替换之前的
        )
        
        logger.info(f"房产数据清洗完成，清洗后数据: {len(df)} 条")
        logger.info(f"数据已保存到: {output_path}")
        
        return output_path
    
    def _remove_duplicates(self, df: pd.DataFrame) -> pd.DataFrame:
        """去除重复数据"""
        original_count = len(df)

        # 基于标题去重，如果没有position_info则只用title
        if 'position_info' in df.columns:
            df = df.drop_duplicates(subset=['title', 'position_info'], keep='first')
        else:
            df = df.drop_duplicates(subset=['title'], keep='first')

        removed_count = original_count - len(df)
        if removed_count > 0:
            logger.info(f"去除重复数据: {removed_count} 条")

        return df

    def _extract_community_info(self, df: pd.DataFrame) -> pd.DataFrame:
        """从house_info字段中提取小区信息"""
        logger.info("提取小区信息...")

        if 'house_info' in df.columns:
            # 从house_info中提取小区名称
            # 格式通常是：浦东-区域-小区名称/面积/朝向/房型/楼层
            df['community_extracted'] = df['house_info'].astype(str).str.extract(r'浦东-[^-]+-([^/]+)')

            # 如果原来有community字段但为空，用提取的信息填充
            if 'community' in df.columns:
                df['community'] = df['community'].fillna(df['community_extracted'])
            else:
                df['community'] = df['community_extracted']

            # 清理小区名称
            df['community'] = df['community'].astype(str).str.strip()

            # 提取面积信息
            df['area_extracted'] = df['house_info'].astype(str).str.extract(r'([0-9.]+)㎡')
            if 'area' not in df.columns or df['area'].isna().all():
                df['area'] = df['area_extracted']

            # 提取房型信息
            df['rooms_extracted'] = df['house_info'].astype(str).str.extract(r'([0-9]+室[0-9]+厅[0-9]*卫?)')
            if 'rooms' not in df.columns or df['rooms'].isna().all():
                df['rooms'] = df['rooms_extracted']

            # 提取区域信息
            df['district_extracted'] = df['house_info'].astype(str).str.extract(r'浦东-([^-]+)-')
            if 'district' not in df.columns or df['district'].isna().all():
                df['district'] = '浦东新区'  # 统一设置为浦东新区

            # 删除临时列
            df.drop(columns=['community_extracted', 'area_extracted', 'rooms_extracted', 'district_extracted'],
                   inplace=True, errors='ignore')

        logger.info(f"小区信息提取完成，有效小区数: {df['community'].notna().sum()}")
        return df

    def _clean_text_fields(self, df: pd.DataFrame) -> pd.DataFrame:
        """清洗文本字段"""
        text_columns = ['title', 'house_info', 'position_info', 'community', 'district']
        
        for col in text_columns:
            if col in df.columns:
                # 去除多余空格和特殊字符
                df[col] = df[col].astype(str).str.strip()
                df[col] = df[col].str.replace(r'\s+', ' ', regex=True)
                df[col] = df[col].replace(['nan', 'None', ''], np.nan)
        
        logger.debug("文本字段清洗完成")
        return df
    
    def _clean_numeric_fields(self, df: pd.DataFrame) -> pd.DataFrame:
        """清洗数值字段"""
        # 检查实际存在的数值字段
        available_numeric_columns = [col for col in self.numeric_columns if col in df.columns]

        for col in available_numeric_columns:
            # 转换为数值类型
            df[col] = pd.to_numeric(df[col], errors='coerce')

            # 去除异常值
            if col == 'total_price':  # 总价（万元）
                df.loc[df[col] < 50, col] = np.nan  # 低于50万的可能有误
                df.loc[df[col] > 10000, col] = np.nan  # 高于1亿的可能有误
            elif col == 'unit_price':  # 单价（元/平米）
                df.loc[df[col] < 5000, col] = np.nan  # 低于5000元/平米的可能有误
                df.loc[df[col] > 200000, col] = np.nan  # 高于20万元/平米的可能有误
            elif col == 'area_numeric':  # 面积（平米）
                df.loc[df[col] < 10, col] = np.nan  # 低于10平米的可能有误
                df.loc[df[col] > 1000, col] = np.nan  # 高于1000平米的可能有误

        logger.debug("数值字段清洗完成")
        return df
    
    def _standardize_area_info(self, df: pd.DataFrame) -> pd.DataFrame:
        """标准化面积信息"""
        if 'area' in df.columns:
            # 提取面积数值
            df['area_numeric'] = df['area'].astype(str).str.extract(r'(\d+\.?\d*)').astype(float)
            
            # 去除异常面积值
            df.loc[df['area_numeric'] < 10, 'area_numeric'] = np.nan  # 小于10平米
            df.loc[df['area_numeric'] > 1000, 'area_numeric'] = np.nan  # 大于1000平米
        
        logger.debug("面积信息标准化完成")
        return df
    
    def _geocode_addresses(self, df: pd.DataFrame) -> pd.DataFrame:
        """地理编码地址"""
        # 如果数据为空，直接返回
        if len(df) == 0:
            logger.warning("数据为空，跳过地理编码")
            return df

        # 检查是否已有经纬度信息
        if 'longitude' in df.columns and 'latitude' in df.columns:
            valid_coords = df[['longitude', 'latitude']].notna().all(axis=1).sum()
            logger.info(f"已有坐标信息: {valid_coords}/{len(df)} 条")

            # 避免除零错误
            if len(df) > 0 and valid_coords / len(df) > 0.8:  # 如果80%以上有坐标，跳过地理编码
                logger.info("大部分数据已有坐标，跳过地理编码")
                return df
        
        # 准备地址列表
        addresses = self._prepare_addresses(df)
        
        # 批量地理编码
        logger.info("开始批量地理编码")
        coords_list = batch_geocode_addresses(addresses, city="上海", delay=0.3)
        
        # 添加坐标信息
        longitudes = []
        latitudes = []
        
        for coords in coords_list:
            if coords:
                longitudes.append(coords[0])
                latitudes.append(coords[1])
            else:
                longitudes.append(np.nan)
                latitudes.append(np.nan)
        
        df['longitude'] = longitudes
        df['latitude'] = latitudes
        
        # 统计成功率
        success_count = sum(1 for coords in coords_list if coords is not None)
        logger.info(f"地理编码完成，成功率: {success_count}/{len(addresses)} ({success_count/len(addresses)*100:.1f}%)")
        
        return df
    
    def _prepare_addresses(self, df: pd.DataFrame) -> List[str]:
        """准备地理编码的地址列表"""
        addresses = []

        for _, row in df.iterrows():
            address_parts = []

            # 优先使用小区名称
            if pd.notna(row.get('community')):
                community = str(row['community']).strip()
                # 过滤掉明显不是地址的内容
                if not any(keyword in community for keyword in ['官方核验', '新上', '精装', '押一付一', '拎包入住', '随时看房', '首次出租']):
                    address_parts.append(community)

            # 添加区域信息
            if pd.notna(row.get('district')):
                address_parts.append(str(row['district']))

            # 添加位置信息
            if pd.notna(row.get('position_info')):
                position = str(row['position_info'])
                # 清理位置信息，去除多余的描述
                position = re.sub(r'距离.*?地铁站.*?米', '', position)
                if position.strip():
                    address_parts.append(position.strip())

            # 如果没有具体地址，尝试从标题中提取地址信息
            if not address_parts and pd.notna(row.get('title')):
                title = str(row['title'])
                # 尝试提取可能的地址信息（小区名等）
                # 这里可以添加更复杂的地址提取逻辑
                if '小区' in title or '公寓' in title or '花园' in title:
                    address_parts.append(title)

            # 构建完整地址，如果没有有效地址，使用默认的浦东新区
            if address_parts:
                address = '上海市 ' + ' '.join(address_parts)
            else:
                address = '上海市浦东新区陆家嘴'  # 默认地址

            addresses.append(address)

        return addresses
    
    def _add_derived_fields(self, df: pd.DataFrame) -> pd.DataFrame:
        """添加衍生字段"""
        # 计算单价（如果缺失）
        if 'unit_price' not in df.columns or df['unit_price'].isna().sum() > 0:
            calculated_unit_price = None

            # 优先使用总价计算单价
            if 'total_price' in df.columns and 'area_numeric' in df.columns:
                # 总价（万元）转换为单价（元/平米）
                calculated_unit_price = (df['total_price'] * 10000) / df['area_numeric']
            # 如果没有总价和面积，无法计算单价
            # 注释掉租金相关计算，因为我们处理的是二手房数据，没有租金信息

            if calculated_unit_price is not None:
                if 'unit_price' not in df.columns:
                    df['unit_price'] = calculated_unit_price
                else:
                    # 填补缺失的单价
                    df['unit_price'] = df['unit_price'].fillna(calculated_unit_price)
        
        # 添加价格等级
        if 'unit_price' in df.columns:
            df['price_level'] = pd.cut(
                df['unit_price'], 
                bins=[0, 30000, 50000, 80000, 120000, float('inf')],
                labels=['低价', '中低价', '中价', '中高价', '高价']
            )
        
        # 添加房型分类
        if 'rooms' in df.columns:
            df['room_type'] = df['rooms'].astype(str).str.extract(r'(\d+)室').astype(float)
            df['room_category'] = pd.cut(
                df['room_type'],
                bins=[0, 1, 2, 3, 4, float('inf')],
                labels=['1室', '2室', '3室', '4室', '5室+']
            )
        
        logger.debug("衍生字段添加完成")
        return df
    
    def _filter_valid_records(self, df: pd.DataFrame) -> pd.DataFrame:
        """过滤有效记录"""
        original_count = len(df)
        
        # 必须有标题
        df = df[df['title'].notna()]
        
        # 必须有价格信息（总价或单价）
        has_price = (df['total_price'].notna()) | (df['unit_price'].notna())
        df = df[has_price]
        
        # 必须有坐标信息
        has_coords = df[['longitude', 'latitude']].notna().all(axis=1)
        df = df[has_coords]
        
        filtered_count = original_count - len(df)
        if filtered_count > 0:
            logger.info(f"过滤无效记录: {filtered_count} 条")
        
        return df

    def aggregate_by_community(self, input_file: str = None, output_file: str = None) -> str:
        """
        按小区归类房源数据并计算小区级别统计信息

        Args:
            input_file: 输入的房源数据文件路径
            output_file: 输出的小区聚合数据文件路径

        Returns:
            输出文件路径
        """
        # 设置文件路径
        if not input_file:
            input_file = config.get("PATHS", "processed_housing_data",
                                  "data/processed/housing_cleaned_geocoded.csv")

        if not output_file:
            output_file = config.get("PATHS", "community_aggregated_data",
                                   "data/processed/community_aggregated_housing.csv")

        # 加载数据：优先从数据库加载
        df = load_dataframe_hybrid(
            filepath=get_data_path(input_file),
            table_name='housing_data_cleaned',
            prefer_db=True
        )
        if df is None:
            logger.error(f"无法加载房源数据: {input_file}")
            return ""

        logger.info(f"开始按小区归类房源数据，原始数据: {len(df)} 条")

        # 清理小区名称
        df = self._clean_community_names(df)

        # 按小区分组并计算统计信息
        community_stats = self._calculate_community_statistics(df)

        # 保存聚合后的数据：同时保存到文件和数据库
        output_path = get_data_path(output_file)
        save_dataframe_hybrid(
            df=community_stats,
            filepath=output_path,
            table_name='community_aggregated',
            if_exists='replace'
        )

        logger.info(f"小区数据聚合完成，共 {len(community_stats)} 个小区")
        logger.info(f"数据已保存到: {output_path}")

        return output_path

    def _clean_community_names(self, df: pd.DataFrame) -> pd.DataFrame:
        """清理和标准化小区名称"""
        if 'community' not in df.columns:
            logger.warning("数据中缺少小区字段")
            return df

        # 清理小区名称
        df['community_clean'] = df['community'].astype(str).str.strip()

        # 去除常见的无效小区名称
        invalid_names = ['nan', 'None', '', '未知', '暂无', '其他']
        df = df[~df['community_clean'].isin(invalid_names)]

        # 标准化小区名称（去除多余的描述词）
        df['community_clean'] = df['community_clean'].str.replace(r'（.*?）', '', regex=True)
        df['community_clean'] = df['community_clean'].str.replace(r'\(.*?\)', '', regex=True)
        df['community_clean'] = df['community_clean'].str.replace(r'小区$', '', regex=True)
        df['community_clean'] = df['community_clean'].str.replace(r'公寓$', '', regex=True)
        df['community_clean'] = df['community_clean'].str.strip()

        logger.info(f"清理后有效小区数据: {len(df)} 条，涉及 {df['community_clean'].nunique()} 个小区")

        return df

    def _calculate_community_statistics(self, df: pd.DataFrame) -> pd.DataFrame:
        """计算小区级别的统计信息"""
        logger.info("计算小区统计信息...")

        # 按小区分组
        community_groups = df.groupby('community_clean')

        # 计算各项统计指标
        stats_list = []
        filtered_communities = 0

        for community_name, group in community_groups:
            # 过滤数据量小于5的小区
            if len(group) < 5:
                filtered_communities += 1
                logger.debug(f"过滤小区 {community_name}：数据量不足 ({len(group)} < 5)")
                continue
            stats = {
                'community_name': community_name,
                'housing_count': len(group),
            }

            # 计算坐标（取平均值）
            if 'longitude' in group.columns and 'latitude' in group.columns:
                valid_coords = group[['longitude', 'latitude']].dropna()
                if len(valid_coords) > 0:
                    stats['longitude'] = valid_coords['longitude'].mean()
                    stats['latitude'] = valid_coords['latitude'].mean()
                    stats['coord_count'] = len(valid_coords)
                else:
                    stats['longitude'] = np.nan
                    stats['latitude'] = np.nan
                    stats['coord_count'] = 0

            # 计算价格统计
            if 'unit_price' in group.columns:
                unit_prices = group['unit_price'].dropna()
                if len(unit_prices) > 0:
                    stats['avg_unit_price'] = unit_prices.mean()
                    stats['median_unit_price'] = unit_prices.median()
                    stats['min_unit_price'] = unit_prices.min()
                    stats['max_unit_price'] = unit_prices.max()
                    stats['unit_price_std'] = unit_prices.std()
                    stats['unit_price_count'] = len(unit_prices)
                else:
                    stats.update({
                        'avg_unit_price': np.nan,
                        'median_unit_price': np.nan,
                        'min_unit_price': np.nan,
                        'max_unit_price': np.nan,
                        'unit_price_std': np.nan,
                        'unit_price_count': 0
                    })

            if 'total_price' in group.columns:
                total_prices = group['total_price'].dropna()
                if len(total_prices) > 0:
                    stats['avg_total_price'] = total_prices.mean()
                    stats['median_total_price'] = total_prices.median()
                    stats['total_price_count'] = len(total_prices)
                else:
                    stats.update({
                        'avg_total_price': np.nan,
                        'median_total_price': np.nan,
                        'total_price_count': 0
                    })

            if 'rental_price' in group.columns:
                rental_prices = group['rental_price'].dropna()
                if len(rental_prices) > 0:
                    stats['avg_rental_price'] = rental_prices.mean()
                    stats['median_rental_price'] = rental_prices.median()
                    stats['rental_price_count'] = len(rental_prices)
                else:
                    stats.update({
                        'avg_rental_price': np.nan,
                        'median_rental_price': np.nan,
                        'rental_price_count': 0
                    })

            # 计算面积统计
            if 'area_numeric' in group.columns:
                areas = group['area_numeric'].dropna()
                if len(areas) > 0:
                    stats['avg_area'] = areas.mean()
                    stats['median_area'] = areas.median()
                    stats['area_count'] = len(areas)
                else:
                    stats.update({
                        'avg_area': np.nan,
                        'median_area': np.nan,
                        'area_count': 0
                    })

            # 房型分布
            if 'room_type' in group.columns:
                room_types = group['room_type'].dropna()
                if len(room_types) > 0:
                    stats['most_common_room_type'] = room_types.mode().iloc[0] if len(room_types.mode()) > 0 else np.nan
                    stats['room_type_variety'] = room_types.nunique()
                else:
                    stats['most_common_room_type'] = np.nan
                    stats['room_type_variety'] = 0

            # 添加其他有用的统计信息
            if 'district' in group.columns:
                districts = group['district'].dropna()
                if len(districts) > 0:
                    stats['district'] = districts.mode().iloc[0] if len(districts.mode()) > 0 else np.nan
                else:
                    stats['district'] = np.nan

            stats_list.append(stats)

        # 转换为DataFrame
        community_df = pd.DataFrame(stats_list)

        # 添加价格等级
        if 'avg_unit_price' in community_df.columns:
            community_df['price_level'] = pd.cut(
                community_df['avg_unit_price'],
                bins=[0, 30000, 50000, 80000, 120000, float('inf')],
                labels=['低价', '中低价', '中价', '中高价', '高价']
            )

        # 计算到CBD的距离
        community_df = self._add_cbd_distance(community_df)

        # 输出过滤统计
        total_communities = len(community_groups)
        valid_communities = len(community_df)
        logger.info(f"小区过滤统计: 总计 {total_communities} 个小区，过滤 {filtered_communities} 个数据不足的小区")
        logger.info(f"小区统计计算完成，有效小区 {valid_communities} 个（数据量≥5）")

        return community_df

    def _add_cbd_distance(self, df: pd.DataFrame) -> pd.DataFrame:
        """添加到CBD的距离"""
        if 'longitude' not in df.columns or 'latitude' not in df.columns:
            return df

        cbd_lat = config.getfloat("CBD_INFO", "latitude", 31.239638)
        cbd_lng = config.getfloat("CBD_INFO", "longitude", 121.499767)

        distances = []
        for _, row in df.iterrows():
            if pd.notna(row['longitude']) and pd.notna(row['latitude']):
                distance = self._haversine_distance(
                    row['latitude'], row['longitude'], cbd_lat, cbd_lng
                ) / 1000  # 转换为公里
                distances.append(distance)
            else:
                distances.append(np.nan)

        df['distance_to_cbd_km'] = distances

        # 添加距离等级
        df['distance_level'] = pd.cut(
            df['distance_to_cbd_km'],
            bins=[0, 3, 6, 10, 15, float('inf')],
            labels=['很近', '较近', '适中', '较远', '很远']
        )

        return df

    def _haversine_distance(self, lat1: float, lon1: float, lat2: float, lon2: float) -> float:
        """计算两点间的球面距离（米）"""
        from math import radians, cos, sin, asin, sqrt

        # 转换为弧度
        lat1, lon1, lat2, lon2 = map(radians, [lat1, lon1, lat2, lon2])

        # haversine公式
        dlat = lat2 - lat1
        dlon = lon2 - lon1
        a = sin(dlat/2)**2 + cos(lat1) * cos(lat2) * sin(dlon/2)**2
        c = 2 * asin(sqrt(a))

        # 地球半径（米）
        r = 6371000

        return c * r
