# 分析脚本目录

本目录包含项目的各种分析脚本，按功能分类组织。

## 目录结构

```
scripts/
├── README.md                    # 本文件，说明各脚本用途
└── community_analysis/          # 小区分析相关脚本
    ├── run_community_analysis.py           # 小区综合分析主程序
    └── run_community_housing_analysis.py   # 小区房源聚合分析程序
```

## 脚本说明

### 主程序
- **`src/main.py`** - 项目主程序，基于房源的分析方法
  - 爬取房产数据
  - 收集POI和通勤数据
  - 数据清洗和处理
  - 特征工程
  - 生成最终分析结果

### 小区分析脚本

#### `community_analysis/run_community_analysis.py`
**用途**: 基于小区的综合分析系统

**功能**:
- 收集小区基础数据
- 收集小区POI数据
- 计算小区通勤数据
- 计算综合评分并排名
- 生成小区推荐报告

**使用场景**:
- 需要从小区维度进行分析
- 需要小区综合评分和排名
- 需要小区推荐系统

**运行方式**:
```bash
# 完整流程
python scripts/community_analysis/run_community_analysis.py

# 分步执行
python scripts/community_analysis/run_community_analysis.py --step community
python scripts/community_analysis/run_community_analysis.py --step poi
python scripts/community_analysis/run_community_analysis.py --step commute
python scripts/community_analysis/run_community_analysis.py --step score

# 显示前20名小区
python scripts/community_analysis/run_community_analysis.py --top-n 20
```

#### `community_analysis/run_community_housing_analysis.py`
**用途**: 小区房源数据聚合分析系统

**功能**:
- 按小区归类房源数据
- 计算小区平均房价
- 聚合小区POI数据
- 计算小区便利度指数
- 生成小区房源分析报告

**使用场景**:
- 需要从房源数据推导小区特征
- 需要小区房价分析
- 需要小区便利度评估

**运行方式**:
```bash
# 完整流程
python scripts/community_analysis/run_community_housing_analysis.py

# 分步执行
python scripts/community_analysis/run_community_housing_analysis.py --step aggregate
python scripts/community_analysis/run_community_housing_analysis.py --step poi
python scripts/community_analysis/run_community_housing_analysis.py --step analysis

# 指定数据文件
python scripts/community_analysis/run_community_housing_analysis.py --housing-file data/processed/housing_cleaned.csv
```

## 选择使用哪个脚本

### 使用主程序 (`src/main.py`) 当:
- 进行完整的数据收集和分析流程
- 需要房源级别的详细分析
- 进行特征工程和机器学习建模
- 生成最终的研究数据集

### 使用小区分析脚本当:
- 专注于小区维度的分析
- 需要小区排名和推荐
- 进行小区比较研究
- 生成小区投资建议

### 使用房源聚合脚本当:
- 从现有房源数据推导小区特征
- 分析小区房价趋势
- 评估小区便利度
- 进行小区市场分析

## 数据流向

```
房源数据 → 主程序 → 房源级分析结果
    ↓
小区聚合 → 小区分析脚本 → 小区级分析结果
    ↓
POI聚合 → 房源聚合脚本 → 小区房源分析结果
```

## 注意事项

1. **路径配置**: 所有脚本都会自动添加项目根目录到Python路径
2. **配置文件**: 使用统一的 `config/config.ini` 配置文件
3. **日志系统**: 使用统一的日志配置
4. **数据存储**: 所有脚本都使用MySQL数据库存储结果
5. **API限制**: POI和通勤数据收集会受到高德API调用限制

## 执行顺序建议

1. 首先运行主程序收集基础数据
2. 然后根据需要运行相应的分析脚本
3. 各脚本可以独立运行，也可以组合使用
