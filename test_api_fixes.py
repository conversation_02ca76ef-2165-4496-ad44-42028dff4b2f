#!/usr/bin/env python3
"""
测试API修复脚本
专门测试高德API调用和小区名清洗功能
"""

import logging
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.utils.logger_setup import setup_logger

# 设置日志
setup_logger()
logger = logging.getLogger(__name__)

def test_address_cleaning():
    """测试地址清洗功能"""
    logger.info("=" * 50)
    logger.info("测试地址清洗功能")
    logger.info("=" * 50)
    
    try:
        from src.utils.api_helpers import clean_address_for_geocoding
        
        # 测试地址清洗
        test_addresses = [
            "梅园三街坊 官方核验新上近地铁精装",
            "星纪元 公寓拎包入住精装押一付一随时看房",
            "上海市浦东新区陆家嘴",
            "官方核验新上近地铁精装",
            "nan",
            "None",
            ""
        ]
        
        logger.info("测试地址清洗:")
        for addr in test_addresses:
            cleaned = clean_address_for_geocoding(addr)
            logger.info(f"  原始: '{addr}' -> 清洗: '{cleaned}'")
        
        logger.info("✓ 地址清洗功能测试完成")
        return True
        
    except Exception as e:
        logger.error(f"✗ 地址清洗测试失败: {e}")
        return False

def test_community_name_cleaning():
    """测试小区名称清洗"""
    logger.info("=" * 50)
    logger.info("测试小区名称清洗")
    logger.info("=" * 50)
    
    try:
        from src.data_collection.community_poi_collector import CommunityPOICollector
        
        collector = CommunityPOICollector()
        
        # 测试小区名称清洗
        test_names = [
            "梅园三街坊",
            "星纪元公寓",
            "陆家嘴花园小区",
            "东方明珠大厦",
            "世纪公园（小区）",
            "nan",
            "None",
            "",
            "a",  # 太短
            "这是一个非常长的小区名称超过了正常范围"  # 太长
        ]
        
        logger.info("测试小区名称清洗:")
        for name in test_names:
            cleaned = collector._clean_community_name(name)
            logger.info(f"  原始: '{name}' -> 清洗: '{cleaned}'")
        
        logger.info("✓ 小区名称清洗测试完成")
        return True
        
    except Exception as e:
        logger.error(f"✗ 小区名称清洗测试失败: {e}")
        return False

def test_community_extraction():
    """测试小区名称提取"""
    logger.info("=" * 50)
    logger.info("测试小区名称提取")
    logger.info("=" * 50)
    
    try:
        from src.data_collection.community_poi_collector import CommunityPOICollector
        
        collector = CommunityPOICollector()
        
        # 测试从标题提取小区名
        test_titles = [
            "梅园三街坊 官方核验新上近地铁精装",
            "星纪元 公寓拎包入住精装押一付一随时看房",
            "陆家嘴花园 2室1厅 精装修",
            "世纪公园附近 豪华装修",
            "官方核验新上近地铁精装"  # 无有效小区名
        ]
        
        logger.info("测试从标题提取小区名:")
        for title in test_titles:
            community = collector._extract_community_from_title(title)
            logger.info(f"  标题: '{title}'")
            logger.info(f"  小区: '{community}'")
        
        # 测试从房源信息提取小区名
        test_house_infos = [
            "浦东-陆家嘴-梅园三街坊/90㎡/南/2室1厅1卫/高楼层",
            "浦东-世纪公园-星纪元/120㎡/东南/3室2厅2卫/中楼层",
            "浦东-陆家嘴-东方明珠花园/80㎡/西/1室1厅1卫/低楼层",
            "无效格式的房源信息"
        ]
        
        logger.info("测试从房源信息提取小区名:")
        for info in test_house_infos:
            community = collector._extract_community_from_house_info(info)
            logger.info(f"  信息: '{info}'")
            logger.info(f"  小区: '{community}'")
        
        logger.info("✓ 小区名称提取测试完成")
        return True
        
    except Exception as e:
        logger.error(f"✗ 小区名称提取测试失败: {e}")
        return False

def test_api_connection():
    """测试API连接（简单测试）"""
    logger.info("=" * 50)
    logger.info("测试API连接")
    logger.info("=" * 50)
    
    try:
        from src.utils.api_helpers import geocode_address_gaode
        
        # 测试简单的地理编码
        logger.info("测试地理编码: 上海市浦东新区")
        coords = geocode_address_gaode("上海市浦东新区", city="上海")
        
        if coords:
            logger.info(f"✓ 地理编码成功: {coords}")
            return True
        else:
            logger.warning("✗ 地理编码返回空结果")
            return False
            
    except Exception as e:
        logger.error(f"✗ API连接测试失败: {e}")
        return False

def main():
    """主函数"""
    logger.info("开始测试API和地址清洗修复效果...")
    
    results = {
        "地址清洗": test_address_cleaning(),
        "小区名称清洗": test_community_name_cleaning(),
        "小区名称提取": test_community_extraction(),
        "API连接": test_api_connection()
    }
    
    logger.info("=" * 50)
    logger.info("测试结果汇总")
    logger.info("=" * 50)
    
    all_passed = True
    for test_name, result in results.items():
        status = "✓ 通过" if result else "✗ 失败"
        logger.info(f"{test_name}: {status}")
        if not result:
            all_passed = False
    
    if all_passed:
        logger.info("🎉 所有测试通过！API和地址清洗修复成功！")
    else:
        logger.warning("⚠️  部分测试失败，请检查相关配置")
    
    logger.info("=" * 50)
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
