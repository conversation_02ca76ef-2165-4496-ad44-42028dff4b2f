#!/usr/bin/env python3
"""
测试数据流修复脚本
验证数据保存和加载流程
"""

import logging
import sys
import os
import pandas as pd
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.utils.logger_setup import setup_logger
from src.utils.file_io import save_dataframe_hybrid, load_dataframe_hybrid, get_data_path

# 设置日志
setup_logger()
logger = logging.getLogger(__name__)

def test_data_save_load():
    """测试数据保存和加载"""
    logger.info("=" * 50)
    logger.info("测试数据保存和加载流程")
    logger.info("=" * 50)
    
    try:
        # 创建测试数据
        test_data = {
            'title': ['测试房源1', '测试房源2', '测试房源3'],
            'total_price': [500, 600, 700],
            'unit_price': [50000, 60000, 70000],
            'area': [100, 100, 100],
            'community': ['测试小区1', '测试小区2', '测试小区3'],
            'crawl_time': ['2025-06-04 20:00:00'] * 3,
            'source': ['test_scraper'] * 3
        }
        
        df = pd.DataFrame(test_data)
        logger.info(f"创建测试数据: {len(df)} 条记录")
        
        # 测试保存
        test_file = "data/raw/test_housing_data.csv"
        filepath = get_data_path(test_file)
        
        success = save_dataframe_hybrid(
            df=df,
            filepath=filepath,
            table_name='housing_data_test',
            if_exists='replace'
        )
        
        if success:
            logger.info("✓ 数据保存成功")
        else:
            logger.warning("⚠ 数据保存部分失败")
        
        # 检查文件是否存在
        if os.path.exists(filepath):
            logger.info(f"✓ 文件保存成功: {filepath}")
        else:
            logger.error("✗ 文件保存失败")
            return False
        
        # 测试加载 - 优先从文件加载
        loaded_df = load_dataframe_hybrid(
            filepath=filepath,
            table_name='housing_data_test',
            prefer_db=False
        )
        
        if loaded_df is not None:
            logger.info(f"✓ 数据加载成功: {len(loaded_df)} 条记录")
            logger.info(f"  列名: {list(loaded_df.columns)}")
            return True
        else:
            logger.error("✗ 数据加载失败")
            return False
            
    except Exception as e:
        logger.error(f"✗ 测试失败: {e}")
        return False

def test_empty_data_handling():
    """测试空数据处理"""
    logger.info("=" * 50)
    logger.info("测试空数据处理")
    logger.info("=" * 50)
    
    try:
        from src.data_processing.clean_housing_data import HousingDataCleaner
        
        # 创建空DataFrame
        empty_df = pd.DataFrame()
        
        cleaner = HousingDataCleaner()
        
        # 测试地理编码函数对空数据的处理
        result_df = cleaner._geocode_addresses(empty_df)
        
        if len(result_df) == 0:
            logger.info("✓ 空数据处理正常")
            return True
        else:
            logger.error("✗ 空数据处理异常")
            return False
            
    except Exception as e:
        logger.error(f"✗ 空数据处理测试失败: {e}")
        return False

def test_file_priority_loading():
    """测试文件优先加载"""
    logger.info("=" * 50)
    logger.info("测试文件优先加载")
    logger.info("=" * 50)
    
    try:
        # 创建测试文件
        test_data = pd.DataFrame({
            'test_col': [1, 2, 3],
            'source': ['file'] * 3
        })
        
        test_file = "data/raw/test_priority.csv"
        filepath = get_data_path(test_file)
        test_data.to_csv(filepath, index=False, encoding='utf-8-sig')
        
        # 测试优先从文件加载
        loaded_df = load_dataframe_hybrid(
            filepath=filepath,
            table_name='non_existent_table',
            prefer_db=False
        )
        
        if loaded_df is not None and len(loaded_df) == 3:
            logger.info("✓ 文件优先加载正常")
            
            # 清理测试文件
            if os.path.exists(filepath):
                os.remove(filepath)
            
            return True
        else:
            logger.error("✗ 文件优先加载失败")
            return False
            
    except Exception as e:
        logger.error(f"✗ 文件优先加载测试失败: {e}")
        return False

def check_latest_housing_file():
    """检查最新的房产数据文件"""
    logger.info("=" * 50)
    logger.info("检查最新房产数据文件")
    logger.info("=" * 50)
    
    try:
        raw_data_dir = get_data_path("data/raw")
        
        if not os.path.exists(raw_data_dir):
            logger.warning("原始数据目录不存在")
            return False
        
        # 查找所有房产数据文件
        housing_files = []
        for file in os.listdir(raw_data_dir):
            if file.startswith("lianjia_housing_selenium_") and file.endswith(".csv"):
                filepath = os.path.join(raw_data_dir, file)
                housing_files.append((filepath, os.path.getmtime(filepath)))
        
        if not housing_files:
            logger.warning("未找到房产数据文件")
            return False
        
        # 按修改时间排序，获取最新文件
        housing_files.sort(key=lambda x: x[1], reverse=True)
        latest_file = housing_files[0][0]
        
        logger.info(f"最新房产数据文件: {latest_file}")
        
        # 检查文件内容
        try:
            df = pd.read_csv(latest_file, encoding='utf-8-sig')
            logger.info(f"文件包含 {len(df)} 条记录，{len(df.columns)} 个字段")
            logger.info(f"字段名: {list(df.columns)}")
            
            if len(df) > 0:
                logger.info("✓ 最新房产数据文件正常")
                return True
            else:
                logger.warning("⚠ 最新房产数据文件为空")
                return False
                
        except Exception as e:
            logger.error(f"✗ 读取最新房产数据文件失败: {e}")
            return False
            
    except Exception as e:
        logger.error(f"✗ 检查最新房产数据文件失败: {e}")
        return False

def main():
    """主函数"""
    logger.info("开始测试数据流修复效果...")
    
    results = {
        "数据保存加载": test_data_save_load(),
        "空数据处理": test_empty_data_handling(),
        "文件优先加载": test_file_priority_loading(),
        "最新房产文件": check_latest_housing_file()
    }
    
    logger.info("=" * 50)
    logger.info("测试结果汇总")
    logger.info("=" * 50)
    
    all_passed = True
    for test_name, result in results.items():
        status = "✓ 通过" if result else "✗ 失败"
        logger.info(f"{test_name}: {status}")
        if not result:
            all_passed = False
    
    if all_passed:
        logger.info("🎉 所有测试通过！数据流修复成功！")
    else:
        logger.warning("⚠️  部分测试失败，请检查相关配置")
    
    logger.info("=" * 50)
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
