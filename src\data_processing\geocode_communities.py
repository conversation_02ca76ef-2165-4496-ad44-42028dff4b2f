import pandas as pd
import requests
import time
import logging
from pathlib import Path

# Assuming the project structure allows this import path
# Adjust if your project structure is different
from src.utils.config_loader import ConfigLoader
from src.utils.logger_setup import setup_logger

# Setup logger
# If run as a standalone script, logger might not be fully configured
# by a central main.py. Explicitly set it up.
try:
    logger = setup_logger()
except Exception as e:
    # Fallback basic logger if setup_logger fails (e.g. config not found)
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    logger = logging.getLogger(__name__)
    logger.warning(f"Could not initialize logger via setup_logger due to: {e}. Using basicConfig.")

# Constants
GAODE_GEOCODE_API_URL = "https://restapi.amap.com/v3/geocode/geo"
# Assuming Shanghai as the default city. This should be verified or made configurable if data spans multiple cities.
DEFAULT_CITY = "上海市"
API_REQUEST_DELAY_SECONDS = 0.1  # Delay between API calls to respect rate limits
MAX_RETRY_ATTEMPTS = 5  # Maximum retry attempts for failed geocoding
RETRY_DELAY_SECONDS = 2  # Delay between retry attempts

def get_api_key():
    """Reads the Gaode API key from the config file."""
    try:
        config_loader = ConfigLoader()
        api_key = config_loader.get("API_KEYS", "gaode_ak")
        if not api_key:
            logger.error("Gaode API key not found in config.ini ([API_KEYS] section, gaode_ak field).")
            raise ValueError("Gaode API key not found in config.ini.")
        logger.info("Successfully loaded Gaode API key.")
        return api_key
    except Exception as e:
        logger.error(f"Error loading API key: {e}")
        raise

def geocode_address(api_key, address, city=DEFAULT_CITY):
    """
    Geocodes a single address using Gaode API.

    Args:
        api_key (str): Gaode API key.
        address (str): The address to geocode.
        city (str): The city for the address (optional, helps with accuracy).

    Returns:
        tuple: (longitude, latitude, status_remark)
               longitude/latitude are None if geocoding fails.
               status_remark provides info on the geocoding attempt.
    """
    params = {
        "key": api_key,
        "address": address,
        "city": city
    }
    try:
        response = requests.get(GAODE_GEOCODE_API_URL, params=params, timeout=10)
        response.raise_for_status()  # Raise an exception for HTTP errors
        data = response.json()

        if data.get("status") == "1" and data.get("geocodes"):
            location_str = data["geocodes"][0].get("location")
            if location_str:
                lon, lat = location_str.split(',')
                logger.debug(f"Successfully geocoded: {address} -> {lon},{lat}")
                return float(lon), float(lat), "Success"
            else:
                logger.warning(f"Geocoding successful but location not found for: {address}. Response: {data}")
                return None, None, "Success, but no location data"
        else:
            error_info = data.get("info", "Unknown error")
            logger.warning(f"Geocoding failed for: {address}. Status: {data.get('status')}, Info: {error_info}")
            return None, None, f"Failed: {error_info}"

    except requests.exceptions.RequestException as e:
        logger.error(f"API request failed for address '{address}': {e}")
        return None, None, f"API Request Error: {e}"
    except Exception as e:
        logger.error(f"Error processing geocoding response for address '{address}': {e}")
        return None, None, f"Processing Error: {e}"

def geocode_address_with_retry(api_key, address, city=DEFAULT_CITY, max_attempts=MAX_RETRY_ATTEMPTS):
    """
    Geocodes a single address with retry mechanism.

    Args:
        api_key (str): Gaode API key.
        address (str): The address to geocode.
        city (str): The city for the address (optional, helps with accuracy).
        max_attempts (int): Maximum number of retry attempts.

    Returns:
        tuple: (longitude, latitude, status_remark, attempts_used)
               longitude/latitude are None if geocoding fails after all attempts.
               status_remark provides info on the final geocoding attempt.
               attempts_used indicates how many attempts were made.
    """
    for attempt in range(1, max_attempts + 1):
        longitude, latitude, status = geocode_address(api_key, address, city)

        # If successful, return immediately
        if longitude is not None and latitude is not None:
            return longitude, latitude, status, attempt

        # If failed due to rate limit, wait and retry
        if "CUQPS_HAS_EXCEEDED_THE_LIMIT" in status or "RATE_LIMIT" in status.upper():
            if attempt < max_attempts:
                logger.info(f"Rate limit hit for {address}, waiting {RETRY_DELAY_SECONDS}s before retry {attempt + 1}/{max_attempts}")
                time.sleep(RETRY_DELAY_SECONDS)
                continue
        else:
            # For other types of errors, don't retry immediately
            if attempt < max_attempts:
                logger.info(f"Geocoding failed for {address} (attempt {attempt}/{max_attempts}): {status}")
                time.sleep(API_REQUEST_DELAY_SECONDS)
                continue

    # All attempts failed
    return None, None, f"Failed after {max_attempts} attempts: {status}", max_attempts

def main():
    """
    Main function to load data, geocode communities, and save results.
    """
    logger.info("Starting community geocoding process...")

    try:
        api_key = get_api_key()
    except ValueError:
        logger.error("Exiting due to missing API key.")
        return

    # Define file paths
    project_root = Path(__file__).parent.parent.parent

    input_csv_path = project_root / "data" / "raw" / "lianjia_housing_selenium_20250604_211231.csv"
    output_csv_path = project_root / "data" / "processed" / "communities_geocoded.csv"

    # Create output directory if it doesn't exist
    output_csv_path.parent.mkdir(parents=True, exist_ok=True)

    # 直接从原始CSV文件读取数据
    logger.info("从原始CSV文件读取房产数据...")
    try:
        raw_df = pd.read_csv(input_csv_path, encoding='utf-8-sig')
        logger.info(f"成功加载原始数据: {len(raw_df)} 条记录")
        logger.info(f"数据列: {list(raw_df.columns)}")
    except Exception as e:
        logger.error(f"加载原始数据失败: {e}")
        return

    # 检查必要的字段
    required_columns = ['community', 'district']
    missing_columns = [col for col in required_columns if col not in raw_df.columns]
    if missing_columns:
        logger.error(f"缺少必要字段: {missing_columns}")
        return

    # 清洗和提取唯一小区
    logger.info("清洗小区数据...")
    # 去除空值和无效小区名
    valid_df = raw_df.dropna(subset=['community'])
    valid_df = valid_df[valid_df['community'].str.strip() != '']

    # 清洗小区名称
    valid_df = valid_df.copy()
    valid_df['community_clean'] = valid_df['community'].astype(str).str.strip()

    # 去除常见的无效小区名称
    invalid_names = ['nan', 'None', '', '未知', '暂无', '其他', 'null']
    valid_df = valid_df[~valid_df['community_clean'].str.lower().isin([name.lower() for name in invalid_names])]

    # 获取唯一小区列表
    unique_communities = valid_df.groupby(['community_clean', 'district']).size().reset_index(name='house_count')
    unique_communities = unique_communities.rename(columns={'community_clean': 'community_name'})

    logger.info(f"发现 {len(unique_communities)} 个唯一小区")
    logger.info(f"总房源数: {unique_communities['house_count'].sum()}")

    # 显示小区数据样本
    logger.info("\n小区数据样本:")
    logger.info("-" * 80)
    for idx, row in unique_communities.head(10).iterrows():
        logger.info(f"小区: {row['community_name']} (区域: {row['district']}) - 房源数: {row['house_count']}")
    logger.info("")

    num_unique_communities = len(unique_communities)
    logger.info(f"准备对 {num_unique_communities} 个小区进行地理编码.")

    # 初始化地理编码结果
    geocoded_results = []
    for index, row in unique_communities.iterrows():
        geocoded_record = {
            "community_name": row['community_name'],
            "district": row['district'],
            "full_address_for_api": f"{DEFAULT_CITY}{row['district']}{row['community_name']}",
            "longitude": None,
            "latitude": None,
            "geocoding_status": "Pending",
            "house_count": row['house_count'],
            "attempts_used": 0
        }
        geocoded_results.append(geocoded_record)

    # 多轮重试直到全部成功
    round_num = 1
    max_rounds = 10  # 最大重试轮数

    while round_num <= max_rounds:
        # 找出未成功编码的小区
        failed_indices = [i for i, result in enumerate(geocoded_results)
                         if result['longitude'] is None]

        if not failed_indices:
            logger.info("🎉 所有小区地理编码完成！")
            break

        logger.info(f"\n=== 第 {round_num} 轮地理编码 ===")
        logger.info(f"待编码小区数: {len(failed_indices)}")

        successfully_geocoded_this_round = 0

        for i, result_index in enumerate(failed_indices):
            result = geocoded_results[result_index]
            community_name = result['community_name']
            district_name = result['district']
            full_address_for_api = result['full_address_for_api']

            logger.info(f"Processing {i + 1}/{len(failed_indices)}: {community_name} ({district_name})")

            longitude, latitude, status, attempts = geocode_address_with_retry(
                api_key, full_address_for_api, city=DEFAULT_CITY
            )

            # 更新结果
            result['longitude'] = longitude
            result['latitude'] = latitude
            result['geocoding_status'] = status
            result['attempts_used'] += attempts

            if longitude is not None and latitude is not None:
                successfully_geocoded_this_round += 1
                # 输出成功编码的小区名和坐标
                logger.info(f"✓ 地理编码成功:")
                logger.info(f"  小区: {community_name}")
                logger.info(f"  完整地址: {full_address_for_api}")
                logger.info(f"  坐标: 经度={longitude:.6f}, 纬度={latitude:.6f}")
                logger.info(f"  房源数量: {result['house_count']}")
                logger.info(f"  尝试次数: {result['attempts_used']}")
                logger.info("")
            else:
                logger.warning(f"✗ 地理编码失败:")
                logger.warning(f"  小区: {community_name}")
                logger.warning(f"  完整地址: {full_address_for_api}")
                logger.warning(f"  状态: {status}")
                logger.warning(f"  尝试次数: {result['attempts_used']}")
                logger.warning("")

            # Respect API rate limits
            time.sleep(API_REQUEST_DELAY_SECONDS)

        logger.info(f"第 {round_num} 轮完成，本轮成功编码: {successfully_geocoded_this_round} 个小区")

        # 如果本轮没有任何成功，增加等待时间
        if successfully_geocoded_this_round == 0 and len(failed_indices) > 0:
            wait_time = min(30, round_num * 5)  # 逐渐增加等待时间，最多30秒
            logger.info(f"本轮无成功编码，等待 {wait_time} 秒后进行下一轮...")
            time.sleep(wait_time)

        round_num += 1

    # 检查是否还有未成功的
    final_failed = [result for result in geocoded_results if result['longitude'] is None]
    if final_failed:
        logger.warning(f"经过 {max_rounds} 轮重试，仍有 {len(final_failed)} 个小区未能成功编码:")
        for result in final_failed:
            logger.warning(f"  - {result['community_name']}: {result['geocoding_status']}")

    # 清理结果中的attempts_used字段（不需要保存到CSV）
    for result in geocoded_results:
        result.pop('attempts_used', None)

    # Save results
    results_df = pd.DataFrame(geocoded_results)
    try:
        results_df.to_csv(output_csv_path, index=False, encoding='utf-8-sig') # utf-8-sig for Excel compatibility
        logger.info(f"Successfully saved geocoded results to {output_csv_path}")
    except Exception as e:
        logger.error(f"Error saving results to {output_csv_path}: {e}")

    # 计算最终统计
    successfully_geocoded_count = len([r for r in geocoded_results if r['longitude'] is not None])
    failed_count = num_unique_communities - successfully_geocoded_count

    logger.info(f"Geocoding process completed.")
    logger.info(f"Total unique communities processed: {num_unique_communities}")
    logger.info(f"Successfully geocoded: {successfully_geocoded_count}")
    logger.info(f"Failed or no location data: {failed_count}")

    # 打印最终统计摘要
    logger.info("\n最终统计摘要:")
    logger.info("=" * 60)
    logger.info(f"总小区数: {len(results_df)}")
    logger.info(f"成功地理编码: {successfully_geocoded_count}")
    logger.info(f"地理编码成功率: {successfully_geocoded_count/len(results_df)*100:.1f}%")

    # 成功编码的小区房源分布
    successful_geocoded = results_df[results_df['longitude'].notna()]
    if len(successful_geocoded) > 0:
        total_houses = successful_geocoded['house_count'].sum()
        logger.info(f"已编码小区总房源数: {total_houses}")
        logger.info(f"平均每小区房源数: {successful_geocoded['house_count'].mean():.1f}")

        # 显示成功编码的小区示例
        logger.info("\n成功编码的小区示例:")
        logger.info("-" * 50)
        for idx, row in successful_geocoded.head(5).iterrows():
            logger.info(f"小区: {row['community_name']}")
            logger.info(f"  地址: {row['full_address_for_api']}")
            logger.info(f"  坐标: ({row['longitude']:.6f}, {row['latitude']:.6f})")
            logger.info(f"  房源数: {row['house_count']}")
            logger.info("")

    # 如果有失败的小区，显示详细信息
    if failed_count > 0:
        failed_geocoded = results_df[results_df['longitude'].isna()]
        logger.warning(f"\n未成功编码的小区 ({failed_count} 个):")
        logger.warning("-" * 50)
        for idx, row in failed_geocoded.iterrows():
            logger.warning(f"小区: {row['community_name']}")
            logger.warning(f"  地址: {row['full_address_for_api']}")
            logger.warning(f"  状态: {row['geocoding_status']}")
            logger.warning(f"  房源数: {row['house_count']}")
            logger.warning("")

    logger.info(f"地理编码结果已保存到: {output_csv_path}")

if __name__ == "__main__":
    # This allows the script to be run directly.
    # If part of a larger application, the logger might be configured elsewhere.
    # For direct execution, ensure logger is set up.
    # The try-except block for logger setup at the top handles this.
    main()