import pandas as pd
import requests
import time
import logging
from pathlib import Path

# Assuming the project structure allows this import path
# Adjust if your project structure is different
from src.utils.config_loader import ConfigLoader
from src.utils.logger_setup import setup_logger

# Setup logger
# If run as a standalone script, logger might not be fully configured
# by a central main.py. Explicitly set it up.
try:
    logger = setup_logger()
except Exception as e:
    # Fallback basic logger if setup_logger fails (e.g. config not found)
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    logger = logging.getLogger(__name__)
    logger.warning(f"Could not initialize logger via setup_logger due to: {e}. Using basicConfig.")

# Constants
GAODE_GEOCODE_API_URL = "https://restapi.amap.com/v3/geocode/geo"
# Assuming Shanghai as the default city. This should be verified or made configurable if data spans multiple cities.
DEFAULT_CITY = "上海市"
API_REQUEST_DELAY_SECONDS = 0.1  # Delay between API calls to respect rate limits

def get_api_key():
    """Reads the Gaode API key from the config file."""
    try:
        config_loader = ConfigLoader()
        api_key = config_loader.get("amap", "api_key")
        if not api_key:
            logger.error("Gaode API key not found in config.ini ([amap] section, api_key field).")
            raise ValueError("Gaode API key not found in config.ini.")
        logger.info("Successfully loaded Gaode API key.")
        return api_key
    except Exception as e:
        logger.error(f"Error loading API key: {e}")
        raise

def geocode_address(api_key, address, city=DEFAULT_CITY):
    """
    Geocodes a single address using Gaode API.

    Args:
        api_key (str): Gaode API key.
        address (str): The address to geocode.
        city (str): The city for the address (optional, helps with accuracy).

    Returns:
        tuple: (longitude, latitude, status_remark)
               longitude/latitude are None if geocoding fails.
               status_remark provides info on the geocoding attempt.
    """
    params = {
        "key": api_key,
        "address": address,
        "city": city
    }
    try:
        response = requests.get(GAODE_GEOCODE_API_URL, params=params, timeout=10)
        response.raise_for_status()  # Raise an exception for HTTP errors
        data = response.json()

        if data.get("status") == "1" and data.get("geocodes"):
            location_str = data["geocodes"][0].get("location")
            if location_str:
                lon, lat = location_str.split(',')
                logger.debug(f"Successfully geocoded: {address} -> {lon},{lat}")
                return float(lon), float(lat), "Success"
            else:
                logger.warning(f"Geocoding successful but location not found for: {address}. Response: {data}")
                return None, None, "Success, but no location data"
        else:
            error_info = data.get("info", "Unknown error")
            logger.warning(f"Geocoding failed for: {address}. Status: {data.get('status')}, Info: {error_info}")
            return None, None, f"Failed: {error_info}"

    except requests.exceptions.RequestException as e:
        logger.error(f"API request failed for address '{address}': {e}")
        return None, None, f"API Request Error: {e}"
    except Exception as e:
        logger.error(f"Error processing geocoding response for address '{address}': {e}")
        return None, None, f"Processing Error: {e}"

def main():
    """
    Main function to load data, geocode communities, and save results.
    """
    logger.info("Starting community geocoding process...")

    try:
        api_key = get_api_key()
    except ValueError:
        logger.error("Exiting due to missing API key.")
        return

    # Define file paths
    project_root = Path(__file__).parent.parent.parent
    
    # 首先运行数据预处理，生成小区统计信息
    from .preprocess_community_price import CommunityPricePreprocessor
    
    input_csv_path = project_root / "data" / "raw" / "lianjia_housing_selenium_20250604_211231.csv"
    price_stats_path = project_root / "data" / "processed" / "community_price_stats.csv"
    output_csv_path = project_root / "data" / "processed" / "communities_geocoded.csv"

    # Create output directory if it doesn't exist
    output_csv_path.parent.mkdir(parents=True, exist_ok=True)

    # 步骤1: 预处理房价数据，生成小区统计信息
    logger.info("步骤1: 预处理房价数据...")
    preprocessor = CommunityPricePreprocessor()
    price_stats_file = preprocessor.preprocess_housing_data(str(input_csv_path), str(price_stats_path))
    
    if not price_stats_file:
        logger.error("房价数据预处理失败，退出")
        return
    
    # 加载预处理后的小区统计数据
    try:
        community_stats_df = pd.read_csv(price_stats_file)
        logger.info(f"成功加载小区统计数据: {len(community_stats_df)} 个小区")
        logger.info(f"统计数据列: {list(community_stats_df.columns)}")
    except Exception as e:
        logger.error(f"加载小区统计数据失败: {e}")
        return

    # 显示小区数据样本
    logger.info("\n小区数据样本:")
    logger.info("-" * 80)
    for idx, row in community_stats_df.head(5).iterrows():
        logger.info(f"小区: {row['community_name']} (区域: {row['district']})")
        if pd.notna(row.get('avg_unit_price')):
            logger.info(f"  平均单价: {row['avg_unit_price']:.0f} 元/平米")
        if pd.notna(row.get('avg_total_price')):
            logger.info(f"  平均总价: {row['avg_total_price']:.0f} 万元")
        logger.info(f"  房源数量: {row['house_count']}")
        logger.info("")

    num_unique_communities = len(community_stats_df)
    logger.info(f"准备对 {num_unique_communities} 个小区进行地理编码.")

    # 步骤2: 地理编码
    logger.info("步骤2: 开始地理编码...")
    geocoded_results = []
    successfully_geocoded_count = 0

    for index, row in community_stats_df.iterrows():
        community_name = row['community_name']
        district_name = row['district']

        # Construct address string: City + District + Community
        # Gaode API generally performs better with more specific addresses.
        # Example: "上海市浦东新区东南新村"
        full_address_for_api = f"{DEFAULT_CITY}{district_name}{community_name}"
        
        logger.info(f"Processing {index + 1}/{num_unique_communities}: {community_name} ({district_name}) - API Address: {full_address_for_api}")

        longitude, latitude, status = geocode_address(api_key, full_address_for_api, city=DEFAULT_CITY)

        if longitude is not None and latitude is not None:
            successfully_geocoded_count += 1
            # 打印成功编码的示例
            if successfully_geocoded_count <= 5:  # 只打印前5个成功的示例
                logger.info(f"✓ 地理编码成功示例 {successfully_geocoded_count}:")
                logger.info(f"  小区: {community_name}")
                logger.info(f"  地址: {full_address_for_api}")
                logger.info(f"  坐标: ({longitude:.6f}, {latitude:.6f})")
                if pd.notna(row.get('avg_unit_price')):
                    logger.info(f"  平均单价: {row['avg_unit_price']:.0f} 元/平米")
                logger.info("")
        
        # 合并小区统计信息和地理编码结果
        geocoded_record = {
            "community_name": community_name,
            "district": district_name,
            "full_address_for_api": full_address_for_api,
            "longitude": longitude,
            "latitude": latitude,
            "geocoding_status": status,
            # 添加价格统计信息
            "house_count": row.get('house_count', 0),
            "avg_total_price": row.get('avg_total_price'),
            "median_total_price": row.get('median_total_price'),
            "avg_unit_price": row.get('avg_unit_price'),
            "median_unit_price": row.get('median_unit_price'),
            "avg_area": row.get('avg_area'),
            "avg_build_year": row.get('avg_build_year'),
            "main_decoration": row.get('main_decoration', ''),
            "main_layout": row.get('main_layout', '')
        }
        
        geocoded_results.append(geocoded_record)

        # Respect API rate limits
        time.sleep(API_REQUEST_DELAY_SECONDS)

    # Save results
    results_df = pd.DataFrame(geocoded_results)
    try:
        results_df.to_csv(output_csv_path, index=False, encoding='utf-8-sig') # utf-8-sig for Excel compatibility
        logger.info(f"Successfully saved geocoded results to {output_csv_path}")
    except Exception as e:
        logger.error(f"Error saving results to {output_csv_path}: {e}")

    logger.info(f"Geocoding process completed.")
    logger.info(f"Total unique communities processed: {num_unique_communities}")
    logger.info(f"Successfully geocoded: {successfully_geocoded_count}")
    logger.info(f"Failed or no location data: {num_unique_communities - successfully_geocoded_count}")
    
    # 打印最终统计摘要
    logger.info("\n最终统计摘要:")
    logger.info("=" * 60)
    logger.info(f"总小区数: {len(results_df)}")
    logger.info(f"成功地理编码: {successfully_geocoded_count}")
    logger.info(f"地理编码成功率: {successfully_geocoded_count/len(results_df)*100:.1f}%")
    
    # 成功编码的小区价格分布
    successful_geocoded = results_df[results_df['longitude'].notna()]
    if len(successful_geocoded) > 0:
        valid_prices = successful_geocoded['avg_unit_price'].dropna()
        if len(valid_prices) > 0:
            logger.info(f"已编码小区单价范围: {valid_prices.min():.0f} - {valid_prices.max():.0f} 元/平米")
            logger.info(f"已编码小区平均单价: {valid_prices.mean():.0f} 元/平米")

if __name__ == "__main__":
    # This allows the script to be run directly.
    # If part of a larger application, the logger might be configured elsewhere.
    # For direct execution, ensure logger is set up.
    # The try-except block for logger setup at the top handles this.
    main()